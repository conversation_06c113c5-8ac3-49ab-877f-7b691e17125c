# 异常检测损失函数
# 基于VAE-UNET项目的损失函数扩展，专门用于MRI肿瘤异常检测

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from typing import Tuple, Optional

class AnomalyDetectionLoss(nn.Module):
    """
    结合分割、重构和异常检测的组合损失函数
    适用于VAE-UNet架构的MRI肿瘤分割和异常检测
    """
    
    def __init__(self, 
                 alpha: float = 1.0,      # 分割损失权重
                 beta: float = 0.1,       # 重构损失权重
                 gamma: float = 0.01,     # KL散度损失权重
                 delta: float = 0.05,     # 异常检测损失权重
                 use_focal_loss: bool = True,
                 focal_alpha: float = 0.25,
                 focal_gamma: float = 2.0):
        """
        Args:
            alpha: 分割损失权重
            beta: 重构损失权重  
            gamma: KL散度损失权重
            delta: 异常检测损失权重
            use_focal_loss: 是否使用焦点损失处理类别不平衡
            focal_alpha: 焦点损失的alpha参数
            focal_gamma: 焦点损失的gamma参数
        """
        super().__init__()
        
        self.alpha = alpha
        self.beta = beta
        self.gamma = gamma
        self.delta = delta
        
        # 分割损失组件
        self.dice_loss = DiceLoss()
        self.bce_loss = nn.BCEWithLogitsLoss()
        
        if use_focal_loss:
            self.focal_loss = FocalLoss(alpha=focal_alpha, gamma=focal_gamma)
        else:
            self.focal_loss = None
        
        # 重构损失
        self.recon_loss = nn.MSELoss()
        
        # 异常检测损失
        self.anomaly_loss = AnomalyDetectionLoss_Component()
    
    def forward(self, 
                pred_seg: torch.Tensor,           # 分割预测 (B, 1, H, W)
                true_seg: torch.Tensor,           # 真实分割 (B, 1, H, W)
                reconstructed: torch.Tensor,      # 重构图像 (B, C, H, W)
                original: torch.Tensor,           # 原始图像 (B, C, H, W)
                mu: torch.Tensor,                 # VAE均值 (B, latent_dim)
                logvar: torch.Tensor,             # VAE对数方差 (B, latent_dim)
                is_healthy: Optional[torch.Tensor] = None  # 健康标签 (B,)
                ) -> Tuple[torch.Tensor, dict]:
        """
        计算组合损失
        
        Returns:
            total_loss: 总损失
            loss_dict: 各组件损失的字典
        """
        
        # 1. 分割损失
        seg_loss = self._compute_segmentation_loss(pred_seg, true_seg)
        
        # 2. 重构损失
        recon_loss = self.recon_loss(reconstructed, original)
        
        # 3. KL散度损失
        kl_loss = self._compute_kl_loss(mu, logvar)
        
        # 4. 异常检测损失（如果提供了健康标签）
        if is_healthy is not None:
            anomaly_loss = self.anomaly_loss(reconstructed, original, mu, logvar, is_healthy)
        else:
            anomaly_loss = torch.tensor(0.0, device=pred_seg.device)
        
        # 组合总损失
        total_loss = (self.alpha * seg_loss + 
                     self.beta * recon_loss + 
                     self.gamma * kl_loss + 
                     self.delta * anomaly_loss)
        
        # 返回损失字典用于监控
        loss_dict = {
            'total_loss': total_loss.item(),
            'seg_loss': seg_loss.item(),
            'recon_loss': recon_loss.item(),
            'kl_loss': kl_loss.item(),
            'anomaly_loss': anomaly_loss.item()
        }
        
        return total_loss, loss_dict
    
    def _compute_segmentation_loss(self, pred: torch.Tensor, target: torch.Tensor) -> torch.Tensor:
        """计算分割损失（Dice + BCE + 可选的Focal Loss）"""
        dice_loss = self.dice_loss(pred, target)
        bce_loss = self.bce_loss(pred, target)
        
        if self.focal_loss is not None:
            focal_loss = self.focal_loss(pred, target)
            return (dice_loss + bce_loss + focal_loss) / 3
        else:
            return (dice_loss + bce_loss) / 2
    
    def _compute_kl_loss(self, mu: torch.Tensor, logvar: torch.Tensor) -> torch.Tensor:
        """计算KL散度损失"""
        return -0.5 * torch.sum(1 + logvar - mu.pow(2) - logvar.exp()) / mu.size(0)


class DiceLoss(nn.Module):
    """Dice损失函数"""
    
    def __init__(self, smooth: float = 1e-6):
        super().__init__()
        self.smooth = smooth
    
    def forward(self, pred: torch.Tensor, target: torch.Tensor) -> torch.Tensor:
        pred = torch.sigmoid(pred)
        
        # 展平张量
        pred_flat = pred.view(-1)
        target_flat = target.view(-1)
        
        # 计算交集和并集
        intersection = (pred_flat * target_flat).sum()
        union = pred_flat.sum() + target_flat.sum()
        
        # 计算Dice系数
        dice = (2. * intersection + self.smooth) / (union + self.smooth)
        
        return 1 - dice


class FocalLoss(nn.Module):
    """焦点损失，用于处理类别不平衡"""
    
    def __init__(self, alpha: float = 0.25, gamma: float = 2.0):
        super().__init__()
        self.alpha = alpha
        self.gamma = gamma
    
    def forward(self, pred: torch.Tensor, target: torch.Tensor) -> torch.Tensor:
        # 计算BCE损失
        bce_loss = F.binary_cross_entropy_with_logits(pred, target, reduction='none')
        
        # 计算概率
        pt = torch.exp(-bce_loss)
        
        # 计算焦点损失
        focal_loss = self.alpha * (1 - pt) ** self.gamma * bce_loss
        
        return focal_loss.mean()


class AnomalyDetectionLoss_Component(nn.Module):
    """异常检测损失组件"""
    
    def __init__(self, margin: float = 1.0):
        super().__init__()
        self.margin = margin
        self.mse_loss = nn.MSELoss(reduction='none')
    
    def forward(self, 
                reconstructed: torch.Tensor, 
                original: torch.Tensor,
                mu: torch.Tensor,
                logvar: torch.Tensor,
                is_healthy: torch.Tensor) -> torch.Tensor:
        """
        计算异常检测损失
        
        Args:
            reconstructed: 重构图像
            original: 原始图像
            mu: VAE均值
            logvar: VAE对数方差
            is_healthy: 健康标签 (1表示健康，0表示异常)
        """
        
        # 计算重构误差
        recon_error = self.mse_loss(reconstructed, original).mean(dim=[1, 2, 3])  # (B,)
        
        # 计算潜在空间距离（到原点的距离）
        latent_distance = torch.norm(mu, dim=1)  # (B,)
        
        # 组合异常评分
        anomaly_score = recon_error + 0.1 * latent_distance
        
        # 对比损失：健康样本应该有低异常评分，异常样本应该有高异常评分
        healthy_mask = is_healthy.bool()
        anomaly_mask = ~healthy_mask
        
        loss = 0.0
        
        # 健康样本：最小化异常评分
        if healthy_mask.any():
            healthy_loss = anomaly_score[healthy_mask].mean()
            loss += healthy_loss
        
        # 异常样本：最大化异常评分（通过最小化负值）
        if anomaly_mask.any():
            anomaly_loss = torch.clamp(self.margin - anomaly_score[anomaly_mask], min=0).mean()
            loss += anomaly_loss
        
        return loss


class UncertaintyLoss(nn.Module):
    """不确定性损失，用于校准模型的不确定性估计"""
    
    def __init__(self):
        super().__init__()
    
    def forward(self, 
                pred_mean: torch.Tensor, 
                pred_var: torch.Tensor, 
                target: torch.Tensor) -> torch.Tensor:
        """
        计算不确定性损失
        
        Args:
            pred_mean: 预测均值
            pred_var: 预测方差
            target: 真实标签
        """
        
        # 负对数似然损失
        nll_loss = 0.5 * torch.log(2 * np.pi * pred_var) + \
                   0.5 * (target - pred_mean) ** 2 / pred_var
        
        return nll_loss.mean()


# 使用示例
if __name__ == "__main__":
    # 创建损失函数
    criterion = AnomalyDetectionLoss(
        alpha=1.0,    # 分割损失权重
        beta=0.1,     # 重构损失权重
        gamma=0.01,   # KL散度权重
        delta=0.05    # 异常检测权重
    )
    
    # 模拟数据
    batch_size = 4
    channels = 4
    height, width = 256, 256
    latent_dim = 128
    
    pred_seg = torch.randn(batch_size, 1, height, width)
    true_seg = torch.randint(0, 2, (batch_size, 1, height, width)).float()
    reconstructed = torch.randn(batch_size, channels, height, width)
    original = torch.randn(batch_size, channels, height, width)
    mu = torch.randn(batch_size, latent_dim)
    logvar = torch.randn(batch_size, latent_dim)
    is_healthy = torch.randint(0, 2, (batch_size,)).float()
    
    # 计算损失
    total_loss, loss_dict = criterion(
        pred_seg, true_seg, reconstructed, original, mu, logvar, is_healthy
    )
    
    print(f"Total loss: {total_loss.item():.4f}")
    print("Loss components:")
    for key, value in loss_dict.items():
        print(f"  {key}: {value:.4f}")
