# MRI肿瘤分割数据集适配器
# 基于VAE-UNET项目的IDRIDDataset修改

import os
import logging
import numpy as np
import torch
from torch.utils.data import Dataset
from pathlib import Path
from PIL import Image
import nibabel as nib
from typing import Optional, List, Tuple
import cv2
from tqdm import tqdm

class MRITumorDataset(Dataset):
    """
    MRI肿瘤分割数据集，支持多模态MRI输入
    适配BraTS等标准MRI数据集格式
    """
    
    def __init__(self, 
                 base_dir: str, 
                 split: str = 'train',
                 modalities: List[str] = ['T1', 'T1ce', 'T2', 'FLAIR'],
                 scale: float = 1.0,
                 patch_size: Optional[int] = None,
                 max_patients: Optional[int] = None,
                 slice_range: Tuple[int, int] = (60, 120),  # 选择有意义的切片范围
                 min_tumor_ratio: float = 0.01):  # 最小肿瘤占比
        """
        Args:
            base_dir: 数据根目录
            split: 数据集分割 ('train', 'val', 'test')
            modalities: MRI模态列表
            scale: 图像缩放比例
            patch_size: 补丁大小，None表示使用全图
            max_patients: 最大患者数量
            slice_range: 切片范围 (start, end)
            min_tumor_ratio: 包含肿瘤的最小比例
        """
        super().__init__()
        
        self.base_dir = Path(base_dir)
        self.split = split
        self.modalities = modalities
        self.scale = scale
        self.patch_size = patch_size
        self.slice_range = slice_range
        self.min_tumor_ratio = min_tumor_ratio
        
        # 数据目录结构
        self.images_dir = self.base_dir / 'images' / split
        self.masks_dir = self.base_dir / 'masks' / split
        
        # 获取患者ID列表
        self.patient_ids = self._get_patient_ids()
        if max_patients:
            self.patient_ids = self.patient_ids[:max_patients]
            
        # 预处理并提取2D切片
        self.slice_data = []
        self._preprocess_data()
        
        logging.info(f"MRI Dataset initialized: {len(self.slice_data)} slices from {len(self.patient_ids)} patients")
    
    def _get_patient_ids(self) -> List[str]:
        """获取患者ID列表"""
        patient_ids = []
        
        # 假设文件命名格式: PatientXXX_T1.nii.gz
        for file_path in self.images_dir.glob("*_T1.nii.gz"):
            patient_id = file_path.stem.replace("_T1", "")
            
            # 检查是否所有模态都存在
            all_modalities_exist = True
            for modality in self.modalities:
                modality_file = self.images_dir / f"{patient_id}_{modality}.nii.gz"
                if not modality_file.exists():
                    all_modalities_exist = False
                    break
            
            # 检查掩码是否存在
            mask_file = self.masks_dir / f"{patient_id}_seg.nii.gz"
            if not mask_file.exists():
                all_modalities_exist = False
            
            if all_modalities_exist:
                patient_ids.append(patient_id)
        
        logging.info(f"Found {len(patient_ids)} patients with complete data")
        return sorted(patient_ids)
    
    def _preprocess_data(self):
        """预处理数据，提取2D切片"""
        logging.info("Preprocessing MRI data and extracting 2D slices...")
        
        for patient_id in tqdm(self.patient_ids, desc="Processing patients"):
            try:
                # 加载所有模态的图像
                modality_data = {}
                for modality in self.modalities:
                    img_path = self.images_dir / f"{patient_id}_{modality}.nii.gz"
                    img_nii = nib.load(str(img_path))
                    img_data = img_nii.get_fdata()
                    modality_data[modality] = img_data
                
                # 加载掩码
                mask_path = self.masks_dir / f"{patient_id}_seg.nii.gz"
                mask_nii = nib.load(str(mask_path))
                mask_data = mask_nii.get_fdata()
                
                # 提取2D切片
                self._extract_slices(patient_id, modality_data, mask_data)
                
            except Exception as e:
                logging.warning(f"Error processing patient {patient_id}: {e}")
                continue
    
    def _extract_slices(self, patient_id: str, modality_data: dict, mask_data: np.ndarray):
        """从3D数据中提取2D切片"""
        # 获取数据维度 (通常是 H x W x D)
        height, width, depth = mask_data.shape
        
        # 确定切片范围
        start_slice = max(0, self.slice_range[0])
        end_slice = min(depth, self.slice_range[1])
        
        for slice_idx in range(start_slice, end_slice):
            # 提取当前切片的掩码
            mask_slice = mask_data[:, :, slice_idx]
            
            # 检查肿瘤占比
            tumor_ratio = np.sum(mask_slice > 0) / (height * width)
            
            # 只保留包含足够肿瘤的切片（或用于异常检测的正常切片）
            if tumor_ratio >= self.min_tumor_ratio or (self.split == 'train' and tumor_ratio == 0):
                # 提取所有模态的图像切片
                image_slices = []
                for modality in self.modalities:
                    img_slice = modality_data[modality][:, :, slice_idx]
                    # 标准化
                    img_slice = self._normalize_slice(img_slice)
                    image_slices.append(img_slice)
                
                # 合并多模态为多通道图像
                multi_modal_image = np.stack(image_slices, axis=0)  # (C, H, W)
                
                # 处理掩码（二值化）
                binary_mask = (mask_data[:, :, slice_idx] > 0).astype(np.float32)
                binary_mask = binary_mask[np.newaxis, ...]  # (1, H, W)
                
                # 存储切片信息
                self.slice_data.append({
                    'patient_id': patient_id,
                    'slice_idx': slice_idx,
                    'image': torch.from_numpy(multi_modal_image).float(),
                    'mask': torch.from_numpy(binary_mask).float(),
                    'tumor_ratio': tumor_ratio,
                    'is_healthy': tumor_ratio == 0  # 用于异常检测
                })
    
    def _normalize_slice(self, img_slice: np.ndarray) -> np.ndarray:
        """标准化图像切片"""
        # 去除异常值
        img_slice = np.clip(img_slice, 
                           np.percentile(img_slice, 1), 
                           np.percentile(img_slice, 99))
        
        # Z-score标准化
        mean = np.mean(img_slice)
        std = np.std(img_slice)
        if std > 0:
            img_slice = (img_slice - mean) / std
        
        # 缩放到[0, 1]
        img_min, img_max = img_slice.min(), img_slice.max()
        if img_max > img_min:
            img_slice = (img_slice - img_min) / (img_max - img_min)
        
        return img_slice
    
    def __len__(self):
        return len(self.slice_data)
    
    def __getitem__(self, idx):
        """获取单个样本"""
        sample = self.slice_data[idx]
        
        image = sample['image']  # (C, H, W)
        mask = sample['mask']    # (1, H, W)
        
        # 应用缩放
        if self.scale != 1.0:
            new_size = (int(image.shape[1] * self.scale), 
                       int(image.shape[2] * self.scale))
            
            # 使用双线性插值缩放图像
            image = torch.nn.functional.interpolate(
                image.unsqueeze(0), size=new_size, 
                mode='bilinear', align_corners=False
            ).squeeze(0)
            
            # 使用最近邻插值缩放掩码
            mask = torch.nn.functional.interpolate(
                mask.unsqueeze(0), size=new_size, 
                mode='nearest'
            ).squeeze(0)
        
        # 提取补丁（如果指定了patch_size）
        if self.patch_size is not None:
            image, mask = self._extract_patch(image, mask)
        
        return {
            'image': image,
            'mask': mask,
            'patient_id': sample['patient_id'],
            'slice_idx': sample['slice_idx'],
            'tumor_ratio': sample['tumor_ratio'],
            'is_healthy': sample['is_healthy']
        }
    
    def _extract_patch(self, image: torch.Tensor, mask: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        """提取随机补丁"""
        _, h, w = image.shape
        
        if h < self.patch_size or w < self.patch_size:
            # 如果图像小于补丁大小，进行填充
            pad_h = max(0, self.patch_size - h)
            pad_w = max(0, self.patch_size - w)
            
            image = torch.nn.functional.pad(image, (0, pad_w, 0, pad_h))
            mask = torch.nn.functional.pad(mask, (0, pad_w, 0, pad_h))
            h, w = image.shape[1], image.shape[2]
        
        # 随机选择补丁位置
        max_y = h - self.patch_size
        max_x = w - self.patch_size
        
        if self.split == 'train':
            # 训练时随机选择
            y = np.random.randint(0, max_y + 1)
            x = np.random.randint(0, max_x + 1)
        else:
            # 验证/测试时选择中心
            y = max_y // 2
            x = max_x // 2
        
        # 提取补丁
        image_patch = image[:, y:y+self.patch_size, x:x+self.patch_size]
        mask_patch = mask[:, y:y+self.patch_size, x:x+self.patch_size]
        
        return image_patch, mask_patch
    
    def get_healthy_samples(self) -> List[dict]:
        """获取健康样本（用于VAE训练健康组织分布）"""
        return [sample for sample in self.slice_data if sample['is_healthy']]
    
    def get_tumor_samples(self) -> List[dict]:
        """获取包含肿瘤的样本"""
        return [sample for sample in self.slice_data if not sample['is_healthy']]


# 使用示例
if __name__ == "__main__":
    # 创建数据集
    dataset = MRITumorDataset(
        base_dir='./data',
        split='train',
        modalities=['T1', 'T1ce', 'T2', 'FLAIR'],
        patch_size=256,
        max_patients=10
    )
    
    print(f"Dataset size: {len(dataset)}")
    
    # 获取一个样本
    sample = dataset[0]
    print(f"Image shape: {sample['image'].shape}")  # 应该是 (4, 256, 256)
    print(f"Mask shape: {sample['mask'].shape}")    # 应该是 (1, 256, 256)
    print(f"Patient ID: {sample['patient_id']}")
    print(f"Is healthy: {sample['is_healthy']}")
