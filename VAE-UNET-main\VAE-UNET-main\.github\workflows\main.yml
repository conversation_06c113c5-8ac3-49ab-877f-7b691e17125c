name: Publish Docker image

on:
  push:
    branches: master

jobs:
  push_to_registry:
    name: Push Docker image
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v2

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v1
        
      - name: Log in to Docker Hub
        uses: docker/login-action@v1
        with:
          username: milesial
          password: ${{ secrets.DOCKER_PASSWORD }}

      - name: Log in to the Container registry
        uses: docker/login-action@f054a8b539a109f9f41c372932f1ae047eff08c9
        with:
          registry: ghcr.io
          username: ${{ github.repository_owner }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Extract metadata (tags, labels) for Docker
        id: meta
        uses: docker/metadata-action@v3
        with:
          images: milesial/unet

      - name: Build and push Docker image
        id: docker_build
        uses: docker/build-push-action@v2
        with:
          context: .
          push: true
          tags: |
            milesial/unet:latest 
            ghcr.io/milesial/pytorch-unet:latest 
