2025-05-31 20:44:21,693 - INFO - Using Segmentation Model (mode: segmentation)
2025-05-31 20:44:22,082 - INFO - Model parameters: 11,570,310 total, 11,570,310 trainable
2025-05-31 20:44:46,356 - WARNING - Missing segmentation file for patient BraTS20_Training_355
2025-05-31 20:44:47,565 - INFO - Loaded 5510 samples for train split
2025-05-31 20:44:47,565 - INFO - Task: segmentation, Multi-class: False
2025-05-31 20:44:52,440 - INFO - Loaded 1875 samples for val split
2025-05-31 20:44:52,440 - INFO - Task: segmentation, Multi-class: False
2025-05-31 20:44:52,441 - INFO - Data loaders created: 2755 train batches, 938 val batches
2025-05-31 20:44:52,441 - INFO - Using combined loss function
2025-05-31 20:44:53,068 - INFO - Using Polynomial LR scheduler (BraTS2018 style)
2025-05-31 20:44:53,071 - INFO - BraTS2018 Inspired Trainer initialized successfully!
2025-05-31 20:44:53,071 - INFO - Starting BraTS2018 Inspired training...
2025-05-31 20:52:41,955 - INFO - Epoch 0:
2025-05-31 20:52:41,955 - INFO -   Train Loss: 0.8720
2025-05-31 20:52:41,955 - INFO -   Val Loss: 1.0006
2025-05-31 20:52:41,956 - INFO -   Val Dice: 0.0001
2025-05-31 20:52:41,956 - INFO -   Val IoU: 0.0000
2025-05-31 20:52:41,956 - INFO -   Learning Rate: 0.000045
2025-05-31 20:52:42,229 - INFO - Saved best model at epoch 0 with Dice: 0.0001
2025-05-31 21:00:59,411 - INFO - Epoch 1:
2025-05-31 21:00:59,411 - INFO -   Train Loss: 0.2924
2025-05-31 21:00:59,411 - INFO -   Val Loss: 1.0013
2025-05-31 21:00:59,411 - INFO -   Val Dice: 0.0000
2025-05-31 21:00:59,411 - INFO -   Val IoU: 0.0000
2025-05-31 21:00:59,411 - INFO -   Learning Rate: 0.***********-05-31 21:11:58,688 - INFO - Using Segmentation Model (mode: segmentation)
2025-05-31 21:11:58,937 - INFO - Model parameters: 11,570,310 total, 11,570,310 trainable
2025-05-31 21:12:23,410 - WARNING - Missing segmentation file for patient BraTS20_Training_355
2025-05-31 21:12:24,650 - INFO - Loaded 5510 samples for train split
2025-05-31 21:12:24,650 - INFO - Task: segmentation, Multi-class: False
2025-05-31 21:12:29,722 - INFO - Loaded 1875 samples for val split
2025-05-31 21:12:29,722 - INFO - Task: segmentation, Multi-class: False
2025-05-31 21:12:29,724 - INFO - Data loaders created: 2755 train batches, 938 val batches
2025-05-31 21:12:29,724 - INFO - Using combined loss function
2025-05-31 21:12:30,574 - INFO - Using Polynomial LR scheduler (BraTS2018 style)
2025-05-31 21:12:30,578 - INFO - BraTS2018 Inspired Trainer initialized successfully!
2025-05-31 21:12:30,578 - INFO - Starting BraTS2018 Inspired training...
2025-05-31 21:21:40,571 - INFO - Epoch 0:
2025-05-31 21:21:40,571 - INFO -   Train Loss: 0.3894
2025-05-31 21:21:40,572 - INFO -   Val Loss: 0.9981
2025-05-31 21:21:40,573 - INFO -   Val Dice: 0.0114
2025-05-31 21:21:40,573 - INFO -   Val IoU: 0.0000
2025-05-31 21:21:40,573 - INFO -   Learning Rate: 0.000091
2025-05-31 21:21:40,892 - INFO - Saved best model at epoch 0 with Dice: 0.0114
2025-05-31 21:30:34,062 - INFO - Epoch 1:
2025-05-31 21:30:34,062 - INFO -   Train Loss: 0.2973
2025-05-31 21:30:34,063 - INFO -   Val Loss: 1.0002
2025-05-31 21:30:34,063 - INFO -   Val Dice: 0.0014
2025-05-31 21:30:34,063 - INFO -   Val IoU: 0.0000
2025-05-31 21:30:34,063 - INFO -   Learning Rate: 0.000082
