2025-05-31 20:44:21,693 - INFO - Using Segmentation Model (mode: segmentation)
2025-05-31 20:44:22,082 - INFO - Model parameters: 11,570,310 total, 11,570,310 trainable
2025-05-31 20:44:46,356 - WARNING - Missing segmentation file for patient BraTS20_Training_355
2025-05-31 20:44:47,565 - INFO - Loaded 5510 samples for train split
2025-05-31 20:44:47,565 - INFO - Task: segmentation, Multi-class: False
2025-05-31 20:44:52,440 - INFO - Loaded 1875 samples for val split
2025-05-31 20:44:52,440 - INFO - Task: segmentation, Multi-class: False
2025-05-31 20:44:52,441 - INFO - Data loaders created: 2755 train batches, 938 val batches
2025-05-31 20:44:52,441 - INFO - Using combined loss function
2025-05-31 20:44:53,068 - INFO - Using Polynomial LR scheduler (BraTS2018 style)
2025-05-31 20:44:53,071 - INFO - BraTS2018 Inspired Trainer initialized successfully!
2025-05-31 20:44:53,071 - INFO - Starting BraTS2018 Inspired training...
2025-05-31 20:52:41,955 - INFO - Epoch 0:
2025-05-31 20:52:41,955 - INFO -   Train Loss: 0.8720
2025-05-31 20:52:41,955 - INFO -   Val Loss: 1.0006
2025-05-31 20:52:41,956 - INFO -   Val Dice: 0.0001
2025-05-31 20:52:41,956 - INFO -   Val IoU: 0.0000
2025-05-31 20:52:41,956 - INFO -   Learning Rate: 0.000045
2025-05-31 20:52:42,229 - INFO - Saved best model at epoch 0 with Dice: 0.0001
2025-05-31 21:00:59,411 - INFO - Epoch 1:
2025-05-31 21:00:59,411 - INFO -   Train Loss: 0.2924
2025-05-31 21:00:59,411 - INFO -   Val Loss: 1.0013
2025-05-31 21:00:59,411 - INFO -   Val Dice: 0.0000
2025-05-31 21:00:59,411 - INFO -   Val IoU: 0.0000
2025-05-31 21:00:59,411 - INFO -   Learning Rate: 0.***********-05-31 21:11:58,688 - INFO - Using Segmentation Model (mode: segmentation)
2025-05-31 21:11:58,937 - INFO - Model parameters: 11,570,310 total, 11,570,310 trainable
2025-05-31 21:12:23,410 - WARNING - Missing segmentation file for patient BraTS20_Training_355
2025-05-31 21:12:24,650 - INFO - Loaded 5510 samples for train split
2025-05-31 21:12:24,650 - INFO - Task: segmentation, Multi-class: False
2025-05-31 21:12:29,722 - INFO - Loaded 1875 samples for val split
2025-05-31 21:12:29,722 - INFO - Task: segmentation, Multi-class: False
2025-05-31 21:12:29,724 - INFO - Data loaders created: 2755 train batches, 938 val batches
2025-05-31 21:12:29,724 - INFO - Using combined loss function
2025-05-31 21:12:30,574 - INFO - Using Polynomial LR scheduler (BraTS2018 style)
2025-05-31 21:12:30,578 - INFO - BraTS2018 Inspired Trainer initialized successfully!
2025-05-31 21:12:30,578 - INFO - Starting BraTS2018 Inspired training...
2025-05-31 21:21:40,571 - INFO - Epoch 0:
2025-05-31 21:21:40,571 - INFO -   Train Loss: 0.3894
2025-05-31 21:21:40,572 - INFO -   Val Loss: 0.9981
2025-05-31 21:21:40,573 - INFO -   Val Dice: 0.0114
2025-05-31 21:21:40,573 - INFO -   Val IoU: 0.0000
2025-05-31 21:21:40,573 - INFO -   Learning Rate: 0.000091
2025-05-31 21:21:40,892 - INFO - Saved best model at epoch 0 with Dice: 0.0114
2025-05-31 21:30:34,062 - INFO - Epoch 1:
2025-05-31 21:30:34,062 - INFO -   Train Loss: 0.2973
2025-05-31 21:30:34,063 - INFO -   Val Loss: 1.0002
2025-05-31 21:30:34,063 - INFO -   Val Dice: 0.0014
2025-05-31 21:30:34,063 - INFO -   Val IoU: 0.0000
2025-05-31 21:30:34,063 - INFO -   Learning Rate: 0.***********-07-10 09:07:28,332 - INFO - Using Segmentation Model (mode: segmentation)
2025-07-10 09:07:28,461 - INFO - Model parameters: 11,570,310 total, 11,570,310 trainable
2025-07-10 09:07:52,802 - WARNING - Missing segmentation file for patient BraTS20_Training_355
2025-07-10 09:07:54,036 - INFO - Loaded 5510 samples for train split
2025-07-10 09:07:54,036 - INFO - Task: segmentation, Multi-class: False
2025-07-10 09:07:59,026 - INFO - Loaded 1875 samples for val split
2025-07-10 09:07:59,027 - INFO - Task: segmentation, Multi-class: False
2025-07-10 09:07:59,027 - INFO - Data loaders created: 2755 train batches, 938 val batches
2025-07-10 09:07:59,028 - INFO - Using combined loss function
2025-07-10 09:07:59,739 - INFO - Using Polynomial LR scheduler (BraTS2018 style)
2025-07-10 09:07:59,742 - INFO - BraTS2018 Inspired Trainer initialized successfully!
2025-07-10 09:07:59,742 - INFO - Starting BraTS2018 Inspired training...
2025-07-10 09:08:18,556 - INFO - Using Segmentation Model (mode: segmentation)
2025-07-10 09:08:18,748 - INFO - Model parameters: 11,570,310 total, 11,570,310 trainable
2025-07-10 09:08:43,182 - WARNING - Missing segmentation file for patient BraTS20_Training_355
2025-07-10 09:08:44,402 - INFO - Loaded 5510 samples for train split
2025-07-10 09:08:44,402 - INFO - Task: segmentation, Multi-class: False
2025-07-10 09:08:49,467 - INFO - Loaded 1875 samples for val split
2025-07-10 09:08:49,467 - INFO - Task: segmentation, Multi-class: False
2025-07-10 09:08:49,468 - INFO - Data loaders created: 2755 train batches, 938 val batches
2025-07-10 09:08:49,468 - INFO - Using combined loss function
2025-07-10 09:08:50,072 - INFO - Using Polynomial LR scheduler (BraTS2018 style)
2025-07-10 09:08:50,074 - INFO - BraTS2018 Inspired Trainer initialized successfully!
2025-07-10 09:08:50,075 - INFO - Starting BraTS2018 Inspired training...
2025-07-10 09:17:37,950 - INFO - Epoch 0:
2025-07-10 09:17:37,950 - INFO -   Train Loss: 0.3994
2025-07-10 09:17:37,950 - INFO -   Val Loss: 0.9237
2025-07-10 09:17:37,950 - INFO -   Val Dice: 0.1269
2025-07-10 09:17:37,950 - INFO -   Val IoU: 0.0000
2025-07-10 09:17:37,950 - INFO -   Learning Rate: 0.***********-07-10 09:17:38,231 - INFO - Saved best model at epoch 0 with Dice: 0.1269
2025-07-10 17:29:05,533 - INFO - Using Segmentation Model (mode: segmentation)
2025-07-10 17:29:05,808 - INFO - Model parameters: 11,570,310 total, 11,570,310 trainable
2025-07-10 17:29:30,866 - WARNING - Missing segmentation file for patient BraTS20_Training_355
2025-07-10 17:29:32,143 - INFO - Loaded 5510 samples for train split
2025-07-10 17:29:32,143 - INFO - Task: segmentation, Multi-class: False
2025-07-10 17:29:37,423 - INFO - Loaded 1875 samples for val split
2025-07-10 17:29:37,423 - INFO - Task: segmentation, Multi-class: False
2025-07-10 17:29:37,423 - INFO - Data loaders created: 2755 train batches, 938 val batches
2025-07-10 17:29:37,423 - INFO - Using combined loss function
2025-07-10 17:29:38,328 - INFO - Using Polynomial LR scheduler (BraTS2018 style)
2025-07-10 17:29:38,335 - INFO - BraTS2018 Inspired Trainer initialized successfully!
2025-07-10 17:29:38,335 - INFO - Starting BraTS2018 Inspired training...
2025-07-10 17:37:58,377 - INFO - Epoch 0:
2025-07-10 17:37:58,377 - INFO -   Train Loss: 0.3859
2025-07-10 17:37:58,377 - INFO -   Val Loss: 0.9787
2025-07-10 17:37:58,378 - INFO -   Val Dice: 0.0386
2025-07-10 17:37:58,378 - INFO -   Val IoU: 0.0000
2025-07-10 17:37:58,378 - INFO -   Learning Rate: 0.000087
2025-07-10 17:37:58,665 - INFO - Saved best model at epoch 0 with Dice: 0.0386
2025-07-10 17:45:57,946 - INFO - Epoch 1:
2025-07-10 17:45:57,946 - INFO -   Train Loss: 0.3048
2025-07-10 17:45:57,946 - INFO -   Val Loss: 0.8932
2025-07-10 17:45:57,947 - INFO -   Val Dice: 0.1353
2025-07-10 17:45:57,947 - INFO -   Val IoU: 0.0075
2025-07-10 17:45:57,947 - INFO -   Learning Rate: 0.000074
2025-07-10 17:45:58,217 - INFO - Saved best model at epoch 1 with Dice: 0.1353
2025-07-10 17:53:50,210 - INFO - Epoch 2:
2025-07-10 17:53:50,210 - INFO -   Train Loss: 0.2779
2025-07-10 17:53:50,210 - INFO -   Val Loss: 1.0014
2025-07-10 17:53:50,210 - INFO -   Val Dice: 0.0006
2025-07-10 17:53:50,210 - INFO -   Val IoU: 0.0000
2025-07-10 17:53:50,210 - INFO -   Learning Rate: 0.000060
2025-07-10 18:01:49,053 - INFO - Epoch 3:
2025-07-10 18:01:49,053 - INFO -   Train Loss: 0.2766
2025-07-10 18:01:49,053 - INFO -   Val Loss: 0.9684
2025-07-10 18:01:49,053 - INFO -   Val Dice: 0.0430
2025-07-10 18:01:49,054 - INFO -   Val IoU: 0.0000
2025-07-10 18:01:49,054 - INFO -   Learning Rate: 0.000047
2025-07-10 18:09:43,808 - INFO - Epoch 4:
2025-07-10 18:09:43,808 - INFO -   Train Loss: 0.2686
2025-07-10 18:09:43,808 - INFO -   Val Loss: 0.9498
2025-07-10 18:09:43,808 - INFO -   Val Dice: 0.0600
2025-07-10 18:09:43,808 - INFO -   Val IoU: 0.0000
2025-07-10 18:09:43,808 - INFO -   Learning Rate: 0.000032
2025-07-10 18:17:35,081 - INFO - Epoch 5:
2025-07-10 18:17:35,081 - INFO -   Train Loss: 0.2602
2025-07-10 18:17:35,081 - INFO -   Val Loss: 0.9416
2025-07-10 18:17:35,081 - INFO -   Val Dice: 0.0795
2025-07-10 18:17:35,081 - INFO -   Val IoU: 0.0000
2025-07-10 18:17:35,082 - INFO -   Learning Rate: 0.000017
2025-07-10 18:25:31,626 - INFO - Epoch 6:
2025-07-10 18:25:31,627 - INFO -   Train Loss: 0.2492
2025-07-10 18:25:31,627 - INFO -   Val Loss: 0.8264
2025-07-10 18:25:31,627 - INFO -   Val Dice: 0.1915
2025-07-10 18:25:31,627 - INFO -   Val IoU: 0.0394
2025-07-10 18:25:31,627 - INFO -   Learning Rate: 0.000000
2025-07-10 18:25:31,909 - INFO - Saved best model at epoch 6 with Dice: 0.1915
2025-07-10 18:25:31,909 - INFO - Training completed!
2025-07-10 18:25:31,909 - INFO - Best validation Dice: 0.1915
2025-07-10 18:25:31,909 - INFO - Total training time: 0.93 hours
