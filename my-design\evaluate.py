"""
脑肿瘤分割模型评估脚本
评估训练好的分割模型性能，包括分割、不确定性量化和异常检测
"""

import os
import logging
import argparse
import torch
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
import json
from tqdm import tqdm
from sklearn.metrics import roc_curve, precision_recall_curve

# 导入自定义模块
from models.model import SegmentationModel
from utils.metrics import ComprehensiveMetrics, UncertaintyMetrics
from data.brats2020_dataset import create_brats2020_loaders


def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )


def load_model(model_path: str, device: torch.device, **model_kwargs):
    """加载训练好的模型"""
    model = SegmentationModel(**model_kwargs).to(device)

    checkpoint = torch.load(model_path, map_location=device)
    model.load_state_dict(checkpoint['model_state_dict'])
    model.eval()

    logging.info(f"Loaded model from {model_path}")
    return model


def evaluate_model(model, data_loader, device, save_dir=None):
    """评估模型性能"""
    model.eval()
    evaluator = ComprehensiveMetrics()
    uncertainty_metrics = UncertaintyMetrics()
    
    all_metrics = []
    all_uncertainties = []
    all_predictions = []
    all_targets = []
    all_reconstructions = []
    all_originals = []
    all_is_healthy = []
    
    with torch.no_grad():
        for batch_idx, batch in enumerate(tqdm(data_loader, desc='Evaluating')):
            # 数据移到设备
            images = batch['image'].to(device)
            masks = batch['mask'].to(device)
            is_healthy = batch['is_healthy'].to(device)
            
            # 前向传播
            seg_pred, reconstruction, mu, logvar, uncertainty = model(images)
            
            # 计算评估指标
            batch_metrics = evaluator.compute_all_metrics(
                seg_pred, masks, uncertainty, reconstruction, images, is_healthy
            )
            all_metrics.append(batch_metrics)
            
            # 收集数据用于后续分析
            all_uncertainties.append(uncertainty.cpu())
            all_predictions.append(seg_pred.cpu())
            all_targets.append(masks.cpu())
            all_reconstructions.append(reconstruction.cpu())
            all_originals.append(images.cpu())
            all_is_healthy.append(is_healthy.cpu())
            
            # 保存一些样本图像
            if save_dir and batch_idx < 5:
                save_sample_results(
                    images, masks, seg_pred, uncertainty, reconstruction,
                    save_dir, batch_idx
                )
    
    # 计算平均指标
    avg_metrics = {}
    for key in all_metrics[0].keys():
        avg_metrics[key] = np.mean([m[key] for m in all_metrics])
    
    # 合并所有数据
    all_uncertainties = torch.cat(all_uncertainties, dim=0)
    all_predictions = torch.cat(all_predictions, dim=0)
    all_targets = torch.cat(all_targets, dim=0)
    all_reconstructions = torch.cat(all_reconstructions, dim=0)
    all_originals = torch.cat(all_originals, dim=0)
    all_is_healthy = torch.cat(all_is_healthy, dim=0)
    
    return avg_metrics, {
        'uncertainties': all_uncertainties,
        'predictions': all_predictions,
        'targets': all_targets,
        'reconstructions': all_reconstructions,
        'originals': all_originals,
        'is_healthy': all_is_healthy
    }


def save_sample_results(images, masks, predictions, uncertainties, reconstructions, save_dir, batch_idx):
    """保存样本结果图像"""
    save_dir = Path(save_dir)
    save_dir.mkdir(parents=True, exist_ok=True)
    
    batch_size = images.size(0)
    
    for i in range(min(batch_size, 2)):  # 每个batch保存前2个样本
        fig, axes = plt.subplots(2, 4, figsize=(16, 8))
        
        # 原始图像 (显示第一个模态)
        img = images[i, 0].cpu().numpy()
        axes[0, 0].imshow(img, cmap='gray')
        axes[0, 0].set_title('Original (T1)')
        axes[0, 0].axis('off')
        
        # 真实掩码
        mask = masks[i, 0].cpu().numpy()
        axes[0, 1].imshow(mask, cmap='hot')
        axes[0, 1].set_title('Ground Truth')
        axes[0, 1].axis('off')
        
        # 预测掩码
        pred = torch.sigmoid(predictions[i, 0]).cpu().numpy()
        axes[0, 2].imshow(pred, cmap='hot')
        axes[0, 2].set_title('Prediction')
        axes[0, 2].axis('off')
        
        # 不确定性
        uncertainty = uncertainties[i, 0].cpu().numpy()
        axes[0, 3].imshow(uncertainty, cmap='viridis')
        axes[0, 3].set_title('Uncertainty')
        axes[0, 3].axis('off')
        
        # 重构图像 (显示第一个模态)
        recon = reconstructions[i, 0].cpu().numpy()
        axes[1, 0].imshow(recon, cmap='gray')
        axes[1, 0].set_title('Reconstruction (T1)')
        axes[1, 0].axis('off')
        
        # 重构误差
        recon_error = np.abs(img - recon)
        axes[1, 1].imshow(recon_error, cmap='hot')
        axes[1, 1].set_title('Reconstruction Error')
        axes[1, 1].axis('off')
        
        # 预测二值化
        pred_binary = (pred > 0.5).astype(float)
        axes[1, 2].imshow(pred_binary, cmap='hot')
        axes[1, 2].set_title('Binary Prediction')
        axes[1, 2].axis('off')
        
        # 叠加显示
        overlay = img.copy()
        overlay[mask > 0.5] = 1.0  # 真实肿瘤区域
        axes[1, 3].imshow(overlay, cmap='gray')
        axes[1, 3].contour(pred_binary, colors='red', linewidths=1)
        axes[1, 3].set_title('Overlay')
        axes[1, 3].axis('off')
        
        plt.tight_layout()
        plt.savefig(save_dir / f'sample_batch{batch_idx}_item{i}.png', dpi=150, bbox_inches='tight')
        plt.close()


def plot_reliability_diagram(uncertainties, predictions, targets, save_path):
    """绘制可靠性图"""
    uncertainty_metrics = UncertaintyMetrics()
    
    bin_confidences, bin_accuracies, bin_counts = uncertainty_metrics.reliability_diagram(
        uncertainties, predictions, targets, n_bins=10
    )
    
    plt.figure(figsize=(8, 6))
    
    # 绘制可靠性图
    plt.plot([0, 1], [0, 1], 'k--', label='Perfect Calibration')
    plt.plot(bin_confidences, bin_accuracies, 'ro-', label='Model Calibration')
    
    # 添加bin计数作为条形图
    plt.bar(bin_confidences, bin_counts / np.sum(bin_counts), 
            alpha=0.3, width=0.08, label='Sample Frequency')
    
    plt.xlabel('Confidence')
    plt.ylabel('Accuracy')
    plt.title('Reliability Diagram')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    plt.savefig(save_path, dpi=150, bbox_inches='tight')
    plt.close()


def plot_uncertainty_analysis(data, save_dir):
    """绘制不确定性分析图"""
    save_dir = Path(save_dir)
    save_dir.mkdir(parents=True, exist_ok=True)
    
    uncertainties = data['uncertainties']
    predictions = data['predictions']
    targets = data['targets']
    is_healthy = data['is_healthy']
    
    # 1. 可靠性图
    plot_reliability_diagram(uncertainties, predictions, targets, 
                           save_dir / 'reliability_diagram.png')
    
    # 2. 不确定性分布
    plt.figure(figsize=(12, 4))
    
    plt.subplot(1, 3, 1)
    healthy_uncertainty = uncertainties[is_healthy].flatten().numpy()
    tumor_uncertainty = uncertainties[~is_healthy].flatten().numpy()
    
    plt.hist(healthy_uncertainty, bins=50, alpha=0.7, label='Healthy', density=True)
    plt.hist(tumor_uncertainty, bins=50, alpha=0.7, label='Tumor', density=True)
    plt.xlabel('Uncertainty')
    plt.ylabel('Density')
    plt.title('Uncertainty Distribution')
    plt.legend()
    
    # 3. 预测误差 vs 不确定性
    plt.subplot(1, 3, 2)
    pred_prob = torch.sigmoid(predictions)
    error = torch.abs(pred_prob - targets).flatten().numpy()
    uncertainty_flat = uncertainties.flatten().numpy()
    
    plt.scatter(uncertainty_flat[::100], error[::100], alpha=0.5, s=1)
    plt.xlabel('Uncertainty')
    plt.ylabel('Prediction Error')
    plt.title('Error vs Uncertainty')
    
    # 4. 重构误差分布
    plt.subplot(1, 3, 3)
    reconstructions = data['reconstructions']
    originals = data['originals']
    
    recon_error = torch.mean((reconstructions - originals) ** 2, dim=[1, 2, 3])
    healthy_recon_error = recon_error[is_healthy].numpy()
    tumor_recon_error = recon_error[~is_healthy].numpy()
    
    plt.hist(healthy_recon_error, bins=30, alpha=0.7, label='Healthy', density=True)
    plt.hist(tumor_recon_error, bins=30, alpha=0.7, label='Tumor', density=True)
    plt.xlabel('Reconstruction Error')
    plt.ylabel('Density')
    plt.title('Reconstruction Error Distribution')
    plt.legend()
    
    plt.tight_layout()
    plt.savefig(save_dir / 'uncertainty_analysis.png', dpi=150, bbox_inches='tight')
    plt.close()


def main():
    parser = argparse.ArgumentParser(description='Evaluate VAE-UNet Model')
    
    # 模型和数据参数
    parser.add_argument('--model_path', type=str, required=True, help='Path to trained model')
    parser.add_argument('--data_dir', type=str, required=True, help='Path to dataset')
    parser.add_argument('--split', type=str, default='test', choices=['train', 'val', 'test'])
    
    # 模型参数
    parser.add_argument('--n_channels', type=int, default=4, help='Number of input channels')
    parser.add_argument('--n_classes', type=int, default=1, help='Number of output classes')
    parser.add_argument('--latent_dim', type=int, default=128, help='Latent dimension')
    parser.add_argument('--use_attention', action='store_true', help='Use attention gates')
    parser.add_argument('--image_size', type=int, default=256, help='Image size')
    
    # 评估参数
    parser.add_argument('--batch_size', type=int, default=4, help='Batch size')
    parser.add_argument('--num_workers', type=int, default=4, help='Number of workers')
    parser.add_argument('--output_dir', type=str, default='./evaluation_results', help='Output directory')
    
    args = parser.parse_args()
    
    # 设置日志
    setup_logging()
    
    # 创建输出目录
    output_dir = Path(args.output_dir)
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # 设置设备
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    logging.info(f'Using device: {device}')
    
    # 加载模型
    model = load_model(
        args.model_path, device,
        input_channels=args.n_channels,
        output_channels=args.n_classes,
        latent_dim=args.latent_dim,
        input_shape=(args.image_size, args.image_size)
    )
    
    # 创建数据加载器
    train_loader, val_loader = create_brats2020_loaders(
        data_dir=args.data_dir,
        batch_size=args.batch_size,
        num_workers=args.num_workers,
        image_size=(args.image_size, args.image_size)
    )
    test_loader = val_loader  # 使用验证集作为测试集
    
    # 选择数据加载器
    if args.split == 'train':
        data_loader = train_loader
    elif args.split == 'val':
        data_loader = val_loader
    else:
        data_loader = test_loader
    
    logging.info(f'Evaluating on {args.split} set with {len(data_loader)} batches')
    
    # 评估模型
    metrics, data = evaluate_model(model, data_loader, device, output_dir / 'samples')
    
    # 打印结果
    evaluator = ComprehensiveMetrics()
    evaluator.print_metrics(metrics)
    
    # 保存指标到文件
    with open(output_dir / 'metrics.json', 'w') as f:
        json.dump(metrics, f, indent=2)
    
    # 绘制分析图
    plot_uncertainty_analysis(data, output_dir / 'plots')
    
    logging.info(f'Evaluation completed. Results saved to {output_dir}')


if __name__ == '__main__':
    main()
