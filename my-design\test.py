"""
脑肿瘤分割模型测试脚本
测试模型功能和性能
"""

import sys
import torch
import numpy as np
import time
from pathlib import Path

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent))

from models.model import SegmentationModel
from utils.loss_functions import VAEUNetLoss, combined_loss_function, my_dice_loss
from utils.metrics import SegmentationMetrics
from utils.scheduler import PolyLR, build_scheduler
from data.brats2020_dataset import BraTS2020Dataset


def test_model_comparison():
    """测试BraTS2018启发的模型"""
    print("=== 模型测试 ===")

    # 测试参数
    input_shape = (240, 240)
    batch_size = 2
    in_channels = 4
    out_channels = 1
    latent_dim = 128

    # 创建测试输入
    x = torch.randn(batch_size, in_channels, *input_shape)

    print(f"输入形状: {x.shape}")

    # 分割模型
    print("\n--- 脑肿瘤分割模型 ---")
    model = SegmentationModel(
        input_shape=input_shape,
        input_channels=in_channels,
        output_channels=out_channels,
        base_channels=32,
        latent_dim=latent_dim,
        dropout_rate=0.2
    )

    start_time = time.time()
    with torch.no_grad():
        seg_out, reconstruction, mu, logvar, uncertainty = model(x)
    inference_time = time.time() - start_time

    model_params = sum(p.numel() for p in model.parameters())

    print(f"参数数量: {model_params:,}")
    print(f"推理时间: {inference_time:.4f}s")
    print(f"分割输出: {seg_out.shape}")
    print(f"重构输出: {reconstruction.shape}")
    print(f"不确定性输出: {uncertainty.shape}")
    print(f"潜在空间维度: mu={mu.shape}, logvar={logvar.shape}")

    return {
        'params': model_params,
        'time': inference_time,
        'model': model
    }


def test_loss_function_comparison():
    """对比损失函数"""
    print("\n=== 损失函数对比测试 ===")
    
    # 创建测试数据
    batch_size = 4
    channels = 4
    height, width = 240, 240
    latent_dim = 128
    
    seg_pred = torch.randn(batch_size, 1, height, width)
    seg_target = torch.randint(0, 2, (batch_size, 1, height, width)).float()
    reconstruction = torch.randn(batch_size, channels, height, width)
    original = torch.randn(batch_size, channels, height, width)
    mu = torch.randn(batch_size, latent_dim)
    logvar = torch.randn(batch_size, latent_dim)
    uncertainty = torch.rand(batch_size, 1, height, width)
    
    print("测试数据形状:")
    print(f"  分割预测: {seg_pred.shape}")
    print(f"  分割目标: {seg_target.shape}")
    print(f"  重构图像: {reconstruction.shape}")
    print(f"  原始图像: {original.shape}")
    
    # 原始损失函数
    print("\n--- 原始VAEUNet损失函数 ---")
    original_criterion = VAEUNetLoss(
        seg_weight=1.0,
        recon_weight=0.1,
        kl_weight=0.01,
        uncertainty_weight=0.05
    )
    
    start_time = time.time()
    orig_loss, orig_loss_dict = original_criterion(
        seg_pred, seg_target, reconstruction, original, mu, logvar, uncertainty
    )
    orig_loss_time = time.time() - start_time
    
    print(f"总损失: {orig_loss.item():.4f}")
    print(f"计算时间: {orig_loss_time:.4f}s")
    for key, value in orig_loss_dict.items():
        print(f"  {key}: {value:.4f}")
    
    # 组合损失函数
    print("\n--- 组合损失函数 ---")

    start_time = time.time()
    combined_loss_dict = combined_loss_function(
        seg_pred, seg_target, reconstruction, original, mu, logvar, weight=0.1
    )
    combined_loss_time = time.time() - start_time

    print(f"总损失: {combined_loss_dict['loss'].item():.4f}")
    print(f"计算时间: {combined_loss_time:.4f}s")
    for key, value in combined_loss_dict.items():
        if isinstance(value, torch.Tensor):
            print(f"  {key}: {value.item():.4f}")
    
    # 单独测试Dice损失
    print("\n--- Dice损失对比 ---")
    
    # 应用sigmoid到预测
    seg_prob = torch.sigmoid(seg_pred)
    
    # 原始Dice计算
    from utils.metrics import SegmentationMetrics
    metrics = SegmentationMetrics()
    orig_dice = metrics.dice_coefficient(seg_prob, seg_target)
    
    # 我的Dice损失
    my_dice_loss_value = my_dice_loss(seg_prob, seg_target)
    my_dice_score = 1 - my_dice_loss_value.item()

    print(f"原始Dice分数: {orig_dice:.4f}")
    print(f"我的Dice分数: {my_dice_score:.4f}")
    print(f"差异: {abs(orig_dice - my_dice_score):.6f}")


def test_scheduler_comparison():
    """对比学习率调度器"""
    print("\n=== 学习率调度器对比测试 ===")
    
    # 创建简单模型用于测试
    model = torch.nn.Linear(10, 1)
    optimizer = torch.optim.Adam(model.parameters(), lr=1e-3)
    
    max_epochs = 100
    
    # 测试不同调度器
    schedulers = {
        'PolyLR (BraTS2018)': PolyLR(optimizer, max_epochs, power=0.9),
        'CosineAnnealing': torch.optim.lr_scheduler.CosineAnnealingLR(optimizer, max_epochs),
        'StepLR': torch.optim.lr_scheduler.StepLR(optimizer, step_size=30, gamma=0.1)
    }
    
    print("学习率调度对比 (选择关键epoch):")
    print("Epoch\t", end="")
    for name in schedulers.keys():
        print(f"{name[:15]:<15}\t", end="")
    print()
    
    test_epochs = [0, 10, 25, 50, 75, 90, 99]
    
    for epoch in test_epochs:
        print(f"{epoch:2d}\t", end="")
        for name, scheduler in schedulers.items():
            # 重置优化器
            for param_group in optimizer.param_groups:
                param_group['lr'] = 1e-3
            
            scheduler.last_epoch = epoch
            lr = scheduler.get_lr()[0]
            print(f"{lr:.6f}\t\t", end="")
        print()


def test_data_loading():
    """测试数据加载"""
    print("\n=== 数据加载测试 ===")
    
    try:
        # 创建数据集
        dataset = BraTS2020Dataset(
            data_dir="./data",
            split='train',
            image_size=(240, 240),
            multi_class=False,
            max_samples_per_patient=1
        )
        
        # 限制数据集大小
        dataset._limit_patients = 2
        dataset.samples = []
        dataset._load_data()
        
        if len(dataset) == 0:
            print("❌ 数据集为空，请检查数据路径")
            return False
        
        print(f"✅ 数据集加载成功，样本数: {len(dataset)}")
        
        # 测试单个样本
        sample = dataset[0]
        print(f"样本形状:")
        print(f"  图像: {sample['image'].shape}")
        print(f"  掩码: {sample['mask'].shape}")
        print(f"  是否健康: {sample['is_healthy']}")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据加载失败: {e}")
        return False


def main():
    """主测试函数"""
    print("脑肿瘤分割模型测试")
    print("=" * 50)
    
    # 设置随机种子
    torch.manual_seed(42)
    np.random.seed(42)
    
    # 测试结果
    results = {}
    
    try:
        # 1. 模型测试
        results['model'] = test_model_comparison()

        # 2. 损失函数对比
        test_loss_function_comparison()

        # 3. 学习率调度器对比
        test_scheduler_comparison()

        # 4. 数据加载测试
        results['data_loading'] = test_data_loading()

        print("\n" + "=" * 50)
        print("测试总结:")

        if 'model' in results:
            model_params = results['model']['params']
            model_time = results['model']['time']
            print(f"✅ 模型测试完成")
            print(f"   参数数量: {model_params:,}")
            print(f"   推理时间: {model_time:.4f}s")

        if results.get('data_loading', False):
            print(f"✅ 数据加载正常")
        else:
            print(f"⚠️  数据加载需要检查")

        print(f"✅ 损失函数对比完成")
        print(f"✅ 学习率调度器对比完成")

        print("\n🎉 所有测试完成！脑肿瘤分割模型验证成功。")
        
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
