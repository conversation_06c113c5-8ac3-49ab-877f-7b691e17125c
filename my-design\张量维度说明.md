# 张量维度详细说明

## 🔢 B×4×240×240 维度解释

### **维度含义**
```
B × 4 × 240 × 240
│   │   │     │
│   │   │     └── Width (宽度): 240像素
│   │   └────── Height (高度): 240像素  
│   └────────── Channels (通道): 4种MRI模态
└────────────── Batch Size (批次): 同时处理的样本数
```

### **具体例子**
```python
# 实际训练中的张量
batch_size = 2  # 同时处理2个患者
input_tensor = torch.randn(2, 4, 240, 240)

# 维度解释：
# [2, 4, 240, 240] 表示：
# - 2个患者的MRI数据
# - 每个患者有4种MRI模态
# - 每种模态的图像尺寸是240×240像素
```

## 🧠 4种MRI模态说明

### **T1, T1ce, T2, FLAIR 是什么？**

| 模态 | 全称 | 特点 | 临床用途 |
|------|------|------|----------|
| **T1** | T1-weighted | 解剖结构清晰 | 显示脑组织结构 |
| **T1ce** | T1-weighted with contrast | 增强对比 | 突出肿瘤边界 |
| **T2** | T2-weighted | 水分敏感 | 显示水肿区域 |
| **FLAIR** | Fluid Attenuated Inversion Recovery | 抑制脑脊液信号 | 清晰显示病变 |

### **为什么需要4种模态？**
```python
# 每种模态提供不同信息
T1_image    # 显示正常脑组织结构
T1ce_image  # 显示肿瘤核心（增强区域）
T2_image    # 显示水肿和肿瘤整体
FLAIR_image # 显示肿瘤边界和水肿

# 组合成4通道输入
input_4d = torch.stack([T1_image, T1ce_image, T2_image, FLAIR_image], dim=1)
# 结果: [batch_size, 4, 240, 240]
```

## 📊 网络中的维度变化

### **完整的维度变化流程**
```python
# 输入层
input: [B, 4, 240, 240]  # 4种MRI模态

# 编码器层级
conv1: [B, 32, 240, 240]   # 第1层卷积
down1: [B, 64, 120, 120]   # 第1次下采样
conv2: [B, 64, 120, 120]   # 第2层卷积
down2: [B, 128, 60, 60]    # 第2次下采样
conv3: [B, 128, 60, 60]    # 第3层卷积
down3: [B, 256, 30, 30]    # 第3次下采样
conv4: [B, 256, 30, 30]    # 瓶颈层 ← 分支点

# 三分支处理
# 1. 分割分支 (保持空间信息)
seg_branch: [B, 256, 30, 30] → [B, 1, 240, 240]

# 2. VAE分支 (全局特征)
vae_branch: [B, 256, 30, 30] → [B, 256, 1, 1] → [B, 128] → [B, 4, 240, 240]

# 3. 不确定性分支 (空间不确定性)
unc_branch: [B, 256, 30, 30] → [B, 1, 30, 30] → [B, 1, 240, 240]
```

## 🔄 批次大小的实际影响

### **不同批次大小的对比**
```python
# 小批次 (内存友好)
batch_size = 2
input_shape = [2, 4, 240, 240]  # 约 1.8 MB

# 中等批次 (平衡性能)
batch_size = 4  
input_shape = [4, 4, 240, 240]  # 约 3.7 MB

# 大批次 (可能内存不足)
batch_size = 8
input_shape = [8, 4, 240, 240]  # 约 7.4 MB
```

### **为什么BraTS使用小批次？**
1. **内存限制**: MRI图像数据量大
2. **GPU限制**: 医学图像处理需要大量显存
3. **质量优先**: 小批次训练更稳定
4. **实际需求**: 临床应用通常单个或少量患者

## 📐 空间维度的意义

### **240×240 像素的选择**
```python
# BraTS原始数据: 240×240×155 (3D体积)
# 我们的处理: 240×240 (2D切片)

# 为什么是240×240？
# 1. BraTS标准尺寸
# 2. 计算效率平衡
# 3. 足够的分辨率显示肿瘤细节
# 4. GPU内存可接受
```

### **下采样的作用**
```python
# 编码器下采样过程
240×240 → 120×120 → 60×60 → 30×30
   ↓         ↓        ↓       ↓
 细节特征   局部特征  区域特征 语义特征

# 瓶颈层 30×30 的优势：
# - 包含全局语义信息
# - 计算量适中
# - 适合VAE潜在空间学习
```

## 💾 内存使用计算

### **各层内存占用** (batch_size=2)
```python
# 输入层
input: [2, 4, 240, 240] = 2×4×240×240×4字节 = 1.84 MB

# 编码器特征
conv1: [2, 32, 240, 240] = 2×32×240×240×4字节 = 14.75 MB
conv2: [2, 64, 120, 120] = 2×64×120×120×4字节 = 7.37 MB  
conv3: [2, 128, 60, 60] = 2×128×60×60×4字节 = 3.69 MB
conv4: [2, 256, 30, 30] = 2×256×30×30×4字节 = 1.84 MB

# 输出层
segmentation: [2, 1, 240, 240] = 2×1×240×240×4字节 = 0.46 MB
reconstruction: [2, 4, 240, 240] = 2×4×240×240×4字节 = 1.84 MB
uncertainty: [2, 1, 240, 240] = 2×1×240×240×4字节 = 0.46 MB

# 总特征图内存: ~32 MB (不包括模型参数)
```

## 🎯 实际代码示例

### **张量创建和处理**
```python
import torch

# 创建模拟输入
batch_size = 2
input_tensor = torch.randn(batch_size, 4, 240, 240)

print(f"输入张量形状: {input_tensor.shape}")
print(f"张量大小: {input_tensor.numel():,} 个元素")
print(f"内存占用: {input_tensor.numel() * 4 / 1024 / 1024:.2f} MB")

# 输出:
# 输入张量形状: torch.Size([2, 4, 240, 240])
# 张量大小: 460,800 个元素  
# 内存占用: 1.76 MB
```

### **实际模型测试**
```python
# 来自你的模型测试结果
model = SegmentationModel()
x = torch.randn(2, 4, 240, 240)  # B=2的输入

with torch.no_grad():
    seg_out, reconstruction, mu, logvar, uncertainty = model(x)

print(f"输入形状: {x.shape}")                    # [2, 4, 240, 240]
print(f"分割输出: {seg_out.shape}")              # [2, 1, 240, 240]  
print(f"重构输出: {reconstruction.shape}")       # [2, 4, 240, 240]
print(f"VAE均值: {mu.shape}")                   # [2, 128]
print(f"VAE方差: {logvar.shape}")               # [2, 128]
print(f"不确定性: {uncertainty.shape}")          # [2, 1, 240, 240]
```

## 📝 总结

**B×4×240×240** 表示：
- **B**: 批次大小（通常2-4，受GPU内存限制）
- **4**: 四种MRI模态（T1, T1ce, T2, FLAIR）
- **240×240**: 图像空间尺寸（BraTS标准）

这种表示方法是深度学习中的标准张量维度记法，帮助理解数据在网络中的流动和变化。
