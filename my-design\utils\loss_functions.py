"""
Loss Functions for VAE-UNet Brain Tumor Segmentation
包含分割损失、重构损失、KL散度损失和不确定性损失的组合
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from typing import Tuple, Optional


def my_dice_loss(pred: torch.Tensor, target: torch.Tensor) -> torch.Tensor:
    """
    我的Dice损失函数
    参考了一些论文的实现，增加了数值稳定性

    Args:
        pred: 预测结果 [B, C, H, W] 或 [B, H, W]
        target: 目标标签 [B, C, H, W] 或 [B, H, W]

    Returns:
        Dice损失值
    """
    smooth = 1.0  # 增加平滑项，提高数值稳定性

    # 确保pred在0-1之间
    pred = torch.sigmoid(pred)

    pred_flat = pred.view(-1)
    target_flat = target.view(-1)
    intersection = (pred_flat * target_flat).sum()
    union = pred_flat.sum() + target_flat.sum()

    dice = (2. * intersection + smooth) / (union + smooth)
    return 1 - dice


def my_vae_loss(recon_x: torch.Tensor, x: torch.Tensor,
               mu: torch.Tensor, logvar: torch.Tensor) -> dict:
    """
    我的VAE损失函数
    改进了重构损失的计算方式

    Args:
        recon_x: 重构图像
        x: 原始图像
        mu: VAE均值
        logvar: VAE对数方差

    Returns:
        损失字典
    """
    loss_dict = {}

    # KL散度损失 - 按批次平均
    batch_size = mu.size(0)
    kl_loss = -0.5 * torch.sum(1 + logvar - mu.pow(2) - logvar.exp())
    loss_dict['kl_loss'] = kl_loss / batch_size

    # 重构损失 - 使用L1+L2组合损失
    mse_loss = F.mse_loss(recon_x, x, reduction='mean')
    l1_loss = F.l1_loss(recon_x, x, reduction='mean')
    loss_dict['recon_loss'] = 0.7 * mse_loss + 0.3 * l1_loss

    return loss_dict


def combined_loss_function(seg_pred: torch.Tensor, seg_target: torch.Tensor,
                          recon_x: torch.Tensor, x: torch.Tensor,
                          mu: torch.Tensor, logvar: torch.Tensor,
                          weight: float = 0.1) -> dict:
    """
    BraTS2018冠军方案的完整损失函数

    Args:
        seg_pred: 分割预测 [B, C, H, W]
        seg_target: 分割目标 [B, C, H, W]
        recon_x: 重构图像
        x: 原始图像
        mu: VAE均值
        logvar: VAE对数方差
        weight: VAE损失权重

    Returns:
        损失字典
    """
    loss_dict = {}

    # 分割损失 - 支持多类别
    if seg_pred.shape[1] > 1:  # 多类别情况
        for i in range(seg_pred.shape[1]):
            loss_dict[f'seg_loss_{i}'] = my_dice_loss(seg_pred[:, i], seg_target[:, i])
    else:  # 二分类情况
        loss_dict['seg_loss'] = my_dice_loss(seg_pred, seg_target)

    # VAE损失
    vae_losses = my_vae_loss(recon_x, x, mu, logvar)
    loss_dict.update(vae_losses)

    # 总损失
    seg_loss_total = sum([v for k, v in loss_dict.items() if 'seg_loss' in k])
    loss_dict['loss'] = seg_loss_total + weight * (loss_dict['recon_loss'] + loss_dict['kl_loss'])

    return loss_dict


class DiceLoss(nn.Module):
    """Dice损失函数，用于分割任务"""
    
    def __init__(self, smooth: float = 1e-6):
        super().__init__()
        self.smooth = smooth
    
    def forward(self, pred: torch.Tensor, target: torch.Tensor) -> torch.Tensor:
        # 处理多类别情况
        if pred.shape[1] > 1:  # 多类别
            pred = torch.softmax(pred, dim=1)
            # 如果目标是单通道的类别索引，转换为one-hot
            if target.shape[1] == 1:
                target = target.squeeze(1).long()
                target_one_hot = torch.zeros_like(pred)
                target_one_hot.scatter_(1, target.unsqueeze(1), 1)
                target = target_one_hot
        else:  # 二分类
            pred = torch.sigmoid(pred)

        # 确保尺寸匹配
        if pred.shape != target.shape:
            # 调整目标尺寸以匹配预测
            target = F.interpolate(target, size=pred.shape[2:], mode='nearest')

        # 展平张量
        pred_flat = pred.view(-1)
        target_flat = target.view(-1)

        # 计算交集和并集
        intersection = (pred_flat * target_flat).sum()
        union = pred_flat.sum() + target_flat.sum()

        # 计算Dice系数
        dice = (2. * intersection + self.smooth) / (union + self.smooth)

        return 1 - dice


class FocalLoss(nn.Module):
    """焦点损失，用于处理类别不平衡"""
    
    def __init__(self, alpha: float = 0.25, gamma: float = 2.0, reduction: str = 'mean'):
        super().__init__()
        self.alpha = alpha
        self.gamma = gamma
        self.reduction = reduction
    
    def forward(self, pred: torch.Tensor, target: torch.Tensor) -> torch.Tensor:
        # 确保尺寸匹配
        if pred.shape != target.shape:
            target = F.interpolate(target, size=pred.shape[2:], mode='nearest')

        # 计算BCE损失
        bce_loss = F.binary_cross_entropy_with_logits(pred, target, reduction='none')

        # 计算概率
        pt = torch.exp(-bce_loss)

        # 计算焦点损失
        focal_loss = self.alpha * (1 - pt) ** self.gamma * bce_loss

        if self.reduction == 'mean':
            return focal_loss.mean()
        elif self.reduction == 'sum':
            return focal_loss.sum()
        else:
            return focal_loss


class TverskyLoss(nn.Module):
    """Tversky损失，Dice损失的泛化版本"""
    
    def __init__(self, alpha: float = 0.3, beta: float = 0.7, smooth: float = 1e-6):
        super().__init__()
        self.alpha = alpha  # 假阳性权重
        self.beta = beta    # 假阴性权重
        self.smooth = smooth
    
    def forward(self, pred: torch.Tensor, target: torch.Tensor) -> torch.Tensor:
        pred = torch.sigmoid(pred)

        # 确保尺寸匹配
        if pred.shape != target.shape:
            target = F.interpolate(target, size=pred.shape[2:], mode='nearest')

        # 展平张量
        pred_flat = pred.view(-1)
        target_flat = target.view(-1)

        # 计算真阳性、假阳性、假阴性
        TP = (pred_flat * target_flat).sum()
        FP = ((1 - target_flat) * pred_flat).sum()
        FN = (target_flat * (1 - pred_flat)).sum()

        # 计算Tversky指数
        tversky = (TP + self.smooth) / (TP + self.alpha * FP + self.beta * FN + self.smooth)

        return 1 - tversky


class CombinedSegmentationLoss(nn.Module):
    """组合分割损失：Dice + BCE + Focal"""
    
    def __init__(self, 
                 dice_weight: float = 0.5,
                 bce_weight: float = 0.3,
                 focal_weight: float = 0.2):
        super().__init__()
        self.dice_weight = dice_weight
        self.bce_weight = bce_weight
        self.focal_weight = focal_weight
        
        self.dice_loss = DiceLoss()
        self.bce_loss = nn.BCEWithLogitsLoss()
        self.focal_loss = FocalLoss()
    
    def forward(self, pred: torch.Tensor, target: torch.Tensor) -> torch.Tensor:
        # 确保尺寸匹配
        if pred.shape != target.shape:
            target = F.interpolate(target, size=pred.shape[2:], mode='nearest')

        dice_loss = self.dice_loss(pred, target)
        bce_loss = self.bce_loss(pred, target)
        focal_loss = self.focal_loss(pred, target)

        total_loss = (self.dice_weight * dice_loss +
                     self.bce_weight * bce_loss +
                     self.focal_weight * focal_loss)

        return total_loss


class ReconstructionLoss(nn.Module):
    """重构损失：MSE + SSIM"""
    
    def __init__(self, mse_weight: float = 0.7, ssim_weight: float = 0.3):
        super().__init__()
        self.mse_weight = mse_weight
        self.ssim_weight = ssim_weight
        self.mse_loss = nn.MSELoss()
    
    def ssim_loss(self, pred: torch.Tensor, target: torch.Tensor, 
                  window_size: int = 11, sigma: float = 1.5) -> torch.Tensor:
        """计算SSIM损失"""
        # 简化的SSIM实现
        mu1 = F.avg_pool2d(pred, window_size, stride=1, padding=window_size//2)
        mu2 = F.avg_pool2d(target, window_size, stride=1, padding=window_size//2)
        
        mu1_sq = mu1.pow(2)
        mu2_sq = mu2.pow(2)
        mu1_mu2 = mu1 * mu2
        
        sigma1_sq = F.avg_pool2d(pred * pred, window_size, stride=1, padding=window_size//2) - mu1_sq
        sigma2_sq = F.avg_pool2d(target * target, window_size, stride=1, padding=window_size//2) - mu2_sq
        sigma12 = F.avg_pool2d(pred * target, window_size, stride=1, padding=window_size//2) - mu1_mu2
        
        C1 = 0.01 ** 2
        C2 = 0.03 ** 2
        
        ssim_map = ((2 * mu1_mu2 + C1) * (2 * sigma12 + C2)) / \
                   ((mu1_sq + mu2_sq + C1) * (sigma1_sq + sigma2_sq + C2))
        
        return 1 - ssim_map.mean()
    
    def forward(self, pred: torch.Tensor, target: torch.Tensor) -> torch.Tensor:
        mse_loss = self.mse_loss(pred, target)
        ssim_loss = self.ssim_loss(pred, target)
        
        return self.mse_weight * mse_loss + self.ssim_weight * ssim_loss


class KLDivergenceLoss(nn.Module):
    """KL散度损失，用于VAE正则化"""
    
    def __init__(self, beta: float = 1.0, free_bits: float = 0.0):
        super().__init__()
        self.beta = beta
        self.free_bits = free_bits
    
    def forward(self, mu: torch.Tensor, logvar: torch.Tensor) -> torch.Tensor:
        # 标准KL散度
        kl_div = -0.5 * torch.sum(1 + logvar - mu.pow(2) - logvar.exp(), dim=1)
        
        # 应用free bits技巧
        if self.free_bits > 0:
            kl_div = torch.clamp(kl_div, min=self.free_bits)
        
        return self.beta * kl_div.mean()


class UncertaintyLoss(nn.Module):
    """不确定性损失，用于校准不确定性估计"""
    
    def __init__(self, lambda_u: float = 0.1):
        super().__init__()
        self.lambda_u = lambda_u
    
    def forward(self, 
                uncertainty: torch.Tensor, 
                pred: torch.Tensor, 
                target: torch.Tensor) -> torch.Tensor:
        """
        计算不确定性损失
        
        Args:
            uncertainty: 预测的不确定性 [B, 1, H, W]
            pred: 分割预测 [B, 1, H, W]
            target: 真实标签 [B, 1, H, W]
        """
        # 计算预测误差
        pred_prob = torch.sigmoid(pred)
        error = torch.abs(pred_prob - target)
        
        # 不确定性应该与误差正相关
        uncertainty_loss = F.mse_loss(uncertainty, error)
        
        # 正则化项：防止不确定性过大
        uncertainty_reg = torch.mean(uncertainty)
        
        return uncertainty_loss + self.lambda_u * uncertainty_reg


class VAEUNetLoss(nn.Module):
    """
    VAE-UNet的组合损失函数
    包含分割损失、重构损失、KL散度损失和不确定性损失
    """
    
    def __init__(self,
                 seg_weight: float = 1.0,      # 分割损失权重
                 recon_weight: float = 0.1,    # 重构损失权重
                 kl_weight: float = 0.01,      # KL散度权重
                 uncertainty_weight: float = 0.05,  # 不确定性损失权重
                 beta_schedule: Optional[str] = None):  # KL权重调度策略
        super().__init__()
        
        self.seg_weight = seg_weight
        self.recon_weight = recon_weight
        self.kl_weight = kl_weight
        self.uncertainty_weight = uncertainty_weight
        self.beta_schedule = beta_schedule
        
        # 损失函数组件
        self.seg_loss = CombinedSegmentationLoss()
        self.recon_loss = ReconstructionLoss()
        self.kl_loss = KLDivergenceLoss()
        self.uncertainty_loss = UncertaintyLoss()
        
        # 用于KL退火
        self.current_epoch = 0
        self.max_epochs = 100
    
    def update_epoch(self, epoch: int, max_epochs: int = None):
        """更新当前epoch，用于KL退火"""
        self.current_epoch = epoch
        if max_epochs is not None:
            self.max_epochs = max_epochs
    
    def get_kl_weight(self) -> float:
        """获取当前的KL权重（支持退火策略）"""
        if self.beta_schedule == 'linear':
            # 线性退火
            return self.kl_weight * min(1.0, self.current_epoch / (self.max_epochs * 0.5))
        elif self.beta_schedule == 'cosine':
            # 余弦退火
            progress = self.current_epoch / self.max_epochs
            return self.kl_weight * (1 - np.cos(progress * np.pi)) / 2
        else:
            return self.kl_weight
    
    def forward(self, 
                seg_pred: torch.Tensor,
                seg_target: torch.Tensor,
                reconstruction: torch.Tensor,
                original: torch.Tensor,
                mu: torch.Tensor,
                logvar: torch.Tensor,
                uncertainty: torch.Tensor) -> Tuple[torch.Tensor, dict]:
        """
        计算总损失
        
        Returns:
            total_loss: 总损失
            loss_dict: 各组件损失的字典
        """
        
        # 1. 分割损失
        seg_loss = self.seg_loss(seg_pred, seg_target)
        
        # 2. 重构损失
        recon_loss = self.recon_loss(reconstruction, original)
        
        # 3. KL散度损失
        current_kl_weight = self.get_kl_weight()
        kl_loss = self.kl_loss(mu, logvar)
        
        # 4. 不确定性损失
        uncertainty_loss = self.uncertainty_loss(uncertainty, seg_pred, seg_target)
        
        # 5. 总损失
        total_loss = (self.seg_weight * seg_loss +
                     self.recon_weight * recon_loss +
                     current_kl_weight * kl_loss +
                     self.uncertainty_weight * uncertainty_loss)
        
        # 损失字典（用于监控）
        loss_dict = {
            'total_loss': total_loss.item(),
            'seg_loss': seg_loss.item(),
            'recon_loss': recon_loss.item(),
            'kl_loss': kl_loss.item(),
            'uncertainty_loss': uncertainty_loss.item(),
            'kl_weight': current_kl_weight
        }
        
        return total_loss, loss_dict


# 使用示例
if __name__ == "__main__":
    # 创建损失函数
    criterion = VAEUNetLoss(
        seg_weight=1.0,
        recon_weight=0.1,
        kl_weight=0.01,
        uncertainty_weight=0.05,
        beta_schedule='linear'
    )
    
    # 模拟数据
    batch_size = 4
    channels = 4
    height, width = 256, 256
    latent_dim = 128
    
    seg_pred = torch.randn(batch_size, 1, height, width)
    seg_target = torch.randint(0, 2, (batch_size, 1, height, width)).float()
    reconstruction = torch.randn(batch_size, channels, height, width)
    original = torch.randn(batch_size, channels, height, width)
    mu = torch.randn(batch_size, latent_dim)
    logvar = torch.randn(batch_size, latent_dim)
    uncertainty = torch.rand(batch_size, 1, height, width)
    
    # 计算损失
    total_loss, loss_dict = criterion(
        seg_pred, seg_target, reconstruction, original, mu, logvar, uncertainty
    )
    
    print(f"Total loss: {total_loss.item():.4f}")
    print("Loss components:")
    for key, value in loss_dict.items():
        print(f"  {key}: {value:.4f}")
    
    # 测试KL退火
    print("\nKL weight schedule:")
    for epoch in range(0, 101, 20):
        criterion.update_epoch(epoch, 100)
        print(f"Epoch {epoch}: KL weight = {criterion.get_kl_weight():.4f}")
