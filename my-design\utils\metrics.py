"""
Evaluation Metrics for Brain Tumor Segmentation and Uncertainty Analysis
包含分割性能指标、不确定性量化指标和异常检测指标
"""

import torch
import torch.nn.functional as F
import numpy as np
from typing import Dict, Tuple, Optional
from sklearn.metrics import roc_auc_score, average_precision_score
import matplotlib.pyplot as plt


class SegmentationMetrics:
    """分割性能评估指标"""

    @staticmethod
    def dice_coefficient(pred: torch.Tensor, target: torch.Tensor, threshold: float = 0.5, smooth: float = 1.0) -> float:
        """计算Dice系数 - 基于BraTS2018冠军方案"""
        # 处理形状不匹配
        if pred.shape != target.shape:
            if len(pred.shape) == 4 and len(target.shape) == 3:
                target = target.unsqueeze(1)
            elif len(pred.shape) == 3 and len(target.shape) == 4:
                pred = pred.unsqueeze(1)

        # 应用sigmoid和阈值（BraTS2018方式）
        pred_flat = (pred.view(-1) > threshold).float()
        target_flat = target.view(-1).float()

        # 计算交集
        intersection = (pred_flat * target_flat).sum()

        # BraTS2018冠军方案的Dice公式
        dice = (2. * intersection + smooth) / (pred_flat.sum() + target_flat.sum() + smooth)
        return dice.item()
    
    @staticmethod
    def iou_score(pred: torch.Tensor, target: torch.Tensor, smooth: float = 1e-6) -> float:
        """计算IoU (Intersection over Union)"""
        pred = torch.sigmoid(pred) > 0.5
        pred = pred.float()
        target = target.float()
        
        intersection = (pred * target).sum()
        union = pred.sum() + target.sum() - intersection
        
        iou = (intersection + smooth) / (union + smooth)
        return iou.item()
    
    @staticmethod
    def sensitivity(pred: torch.Tensor, target: torch.Tensor) -> float:
        """计算敏感性 (召回率)"""
        pred = torch.sigmoid(pred) > 0.5
        pred = pred.float()
        target = target.float()
        
        tp = (pred * target).sum()
        fn = ((1 - pred) * target).sum()
        
        if tp + fn == 0:
            return 1.0
        
        return (tp / (tp + fn)).item()
    
    @staticmethod
    def specificity(pred: torch.Tensor, target: torch.Tensor) -> float:
        """计算特异性"""
        pred = torch.sigmoid(pred) > 0.5
        pred = pred.float()
        target = target.float()
        
        tn = ((1 - pred) * (1 - target)).sum()
        fp = (pred * (1 - target)).sum()
        
        if tn + fp == 0:
            return 1.0
        
        return (tn / (tn + fp)).item()
    
    @staticmethod
    def precision(pred: torch.Tensor, target: torch.Tensor) -> float:
        """计算精确率"""
        pred = torch.sigmoid(pred) > 0.5
        pred = pred.float()
        target = target.float()
        
        tp = (pred * target).sum()
        fp = (pred * (1 - target)).sum()
        
        if tp + fp == 0:
            return 1.0
        
        return (tp / (tp + fp)).item()
    
    @staticmethod
    def hausdorff_distance(pred: torch.Tensor, target: torch.Tensor) -> float:
        """计算Hausdorff距离（简化版本）"""
        try:
            from scipy.spatial.distance import directed_hausdorff
            
            pred = torch.sigmoid(pred) > 0.5
            pred_np = pred.cpu().numpy().squeeze()
            target_np = target.cpu().numpy().squeeze()
            
            # 获取边界点
            pred_points = np.argwhere(pred_np)
            target_points = np.argwhere(target_np)
            
            if len(pred_points) == 0 or len(target_points) == 0:
                return float('inf')
            
            # 计算双向Hausdorff距离
            hd1 = directed_hausdorff(pred_points, target_points)[0]
            hd2 = directed_hausdorff(target_points, pred_points)[0]
            
            return max(hd1, hd2)
        except ImportError:
            # 如果没有scipy，返回简化的距离度量
            return 0.0


class UncertaintyMetrics:
    """不确定性量化评估指标"""
    
    @staticmethod
    def expected_calibration_error(uncertainty: torch.Tensor, 
                                 pred: torch.Tensor, 
                                 target: torch.Tensor, 
                                 n_bins: int = 10) -> float:
        """计算期望校准误差 (ECE)"""
        pred_prob = torch.sigmoid(pred)
        uncertainty = uncertainty.squeeze()
        pred_prob = pred_prob.squeeze()
        target = target.squeeze()
        
        # 将不确定性转换为置信度
        confidence = 1 - uncertainty
        
        # 创建bins
        bin_boundaries = torch.linspace(0, 1, n_bins + 1)
        bin_lowers = bin_boundaries[:-1]
        bin_uppers = bin_boundaries[1:]
        
        ece = 0
        for bin_lower, bin_upper in zip(bin_lowers, bin_uppers):
            # 找到在当前bin中的样本
            in_bin = (confidence > bin_lower.item()) & (confidence <= bin_upper.item())
            prop_in_bin = in_bin.float().mean()
            
            if prop_in_bin.item() > 0:
                # 计算bin中的平均置信度和准确率
                accuracy_in_bin = ((pred_prob > 0.5) == target)[in_bin].float().mean()
                avg_confidence_in_bin = confidence[in_bin].mean()
                
                ece += torch.abs(avg_confidence_in_bin - accuracy_in_bin) * prop_in_bin
        
        return ece.item()
    
    @staticmethod
    def reliability_diagram(uncertainty: torch.Tensor, 
                          pred: torch.Tensor, 
                          target: torch.Tensor, 
                          n_bins: int = 10) -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
        """生成可靠性图的数据"""
        pred_prob = torch.sigmoid(pred)
        uncertainty = uncertainty.squeeze()
        pred_prob = pred_prob.squeeze()
        target = target.squeeze()
        
        confidence = 1 - uncertainty
        
        bin_boundaries = torch.linspace(0, 1, n_bins + 1)
        bin_lowers = bin_boundaries[:-1]
        bin_uppers = bin_boundaries[1:]
        
        bin_confidences = []
        bin_accuracies = []
        bin_counts = []
        
        for bin_lower, bin_upper in zip(bin_lowers, bin_uppers):
            in_bin = (confidence > bin_lower.item()) & (confidence <= bin_upper.item())
            prop_in_bin = in_bin.float().mean()
            
            if prop_in_bin.item() > 0:
                accuracy_in_bin = ((pred_prob > 0.5) == target)[in_bin].float().mean()
                avg_confidence_in_bin = confidence[in_bin].mean()
                
                bin_confidences.append(avg_confidence_in_bin.item())
                bin_accuracies.append(accuracy_in_bin.item())
                bin_counts.append(in_bin.sum().item())
            else:
                bin_confidences.append(0)
                bin_accuracies.append(0)
                bin_counts.append(0)
        
        return np.array(bin_confidences), np.array(bin_accuracies), np.array(bin_counts)
    
    @staticmethod
    def uncertainty_correlation(uncertainty: torch.Tensor, 
                              pred: torch.Tensor, 
                              target: torch.Tensor) -> float:
        """计算不确定性与预测误差的相关性"""
        pred_prob = torch.sigmoid(pred)
        error = torch.abs(pred_prob - target)
        
        uncertainty_flat = uncertainty.flatten()
        error_flat = error.flatten()
        
        # 计算Pearson相关系数
        correlation = torch.corrcoef(torch.stack([uncertainty_flat, error_flat]))[0, 1]
        
        return correlation.item() if not torch.isnan(correlation) else 0.0


class AnomalyDetectionMetrics:
    """异常检测评估指标"""
    
    @staticmethod
    def reconstruction_error(reconstruction: torch.Tensor, original: torch.Tensor) -> float:
        """计算重构误差"""
        mse = F.mse_loss(reconstruction, original)
        return mse.item()
    
    @staticmethod
    def anomaly_auc(anomaly_scores: torch.Tensor, is_anomaly: torch.Tensor) -> float:
        """计算异常检测的AUC"""
        try:
            anomaly_scores_np = anomaly_scores.cpu().numpy().flatten()
            is_anomaly_np = is_anomaly.cpu().numpy().flatten()
            
            if len(np.unique(is_anomaly_np)) < 2:
                return 0.5  # 如果只有一个类别，返回随机性能
            
            auc = roc_auc_score(is_anomaly_np, anomaly_scores_np)
            return auc
        except Exception:
            return 0.5
    
    @staticmethod
    def anomaly_ap(anomaly_scores: torch.Tensor, is_anomaly: torch.Tensor) -> float:
        """计算异常检测的平均精度"""
        try:
            anomaly_scores_np = anomaly_scores.cpu().numpy().flatten()
            is_anomaly_np = is_anomaly.cpu().numpy().flatten()
            
            if len(np.unique(is_anomaly_np)) < 2:
                return np.mean(is_anomaly_np)
            
            ap = average_precision_score(is_anomaly_np, anomaly_scores_np)
            return ap
        except Exception:
            return 0.5


class ComprehensiveMetrics:
    """综合评估指标计算器"""
    
    def __init__(self):
        self.seg_metrics = SegmentationMetrics()
        self.uncertainty_metrics = UncertaintyMetrics()
        self.anomaly_metrics = AnomalyDetectionMetrics()
    
    def compute_all_metrics(self,
                          seg_pred: torch.Tensor,
                          seg_target: torch.Tensor,
                          uncertainty: torch.Tensor,
                          reconstruction: torch.Tensor,
                          original: torch.Tensor,
                          is_healthy: Optional[torch.Tensor] = None) -> Dict[str, float]:
        """计算所有评估指标"""

        metrics = {}

        try:
            # 确保张量形状匹配
            if seg_pred.shape != seg_target.shape:
                # 如果目标是3D而预测是4D，调整目标
                if len(seg_target.shape) == 3 and len(seg_pred.shape) == 4:
                    seg_target = seg_target.unsqueeze(1)
                # 如果目标是4D而预测是3D，调整预测
                elif len(seg_target.shape) == 4 and len(seg_pred.shape) == 3:
                    seg_pred = seg_pred.unsqueeze(1)

            # 分割性能指标
            metrics['dice'] = self.seg_metrics.dice_coefficient(seg_pred, seg_target)
            metrics['iou'] = self.seg_metrics.iou_score(seg_pred, seg_target)
            metrics['sensitivity'] = self.seg_metrics.sensitivity(seg_pred, seg_target)
            metrics['specificity'] = self.seg_metrics.specificity(seg_pred, seg_target)
            metrics['precision'] = self.seg_metrics.precision(seg_pred, seg_target)
            metrics['hausdorff'] = self.seg_metrics.hausdorff_distance(seg_pred, seg_target)
        except Exception as e:
            # 如果计算失败，返回默认值
            metrics['dice'] = 0.0
            metrics['iou'] = 0.0
            metrics['sensitivity'] = 0.0
            metrics['specificity'] = 0.0
            metrics['precision'] = 0.0
            metrics['hausdorff'] = 0.0
        
        # 不确定性量化指标
        try:
            metrics['ece'] = self.uncertainty_metrics.expected_calibration_error(
                uncertainty, seg_pred, seg_target
            )
        except Exception:
            metrics['ece'] = 0.0
        try:
            metrics['uncertainty_correlation'] = self.uncertainty_metrics.uncertainty_correlation(
                uncertainty, seg_pred, seg_target
            )
        except Exception:
            metrics['uncertainty_correlation'] = 0.0

        # 重构误差
        try:
            metrics['reconstruction_error'] = self.anomaly_metrics.reconstruction_error(
                reconstruction, original
            )
        except Exception:
            metrics['reconstruction_error'] = 0.0

        # 异常检测指标（如果提供了健康标签）
        if is_healthy is not None:
            try:
                # 使用重构误差作为异常评分
                recon_error_per_sample = F.mse_loss(
                    reconstruction, original, reduction='none'
                ).mean(dim=[1, 2, 3])

                # 健康样本应该有低重构误差，异常样本应该有高重构误差
                is_anomaly = ~is_healthy.bool()

                metrics['anomaly_auc'] = self.anomaly_metrics.anomaly_auc(
                    recon_error_per_sample, is_anomaly
                )
                metrics['anomaly_ap'] = self.anomaly_metrics.anomaly_ap(
                    recon_error_per_sample, is_anomaly
                )
            except Exception:
                metrics['anomaly_auc'] = 0.0
                metrics['anomaly_ap'] = 0.0
        
        return metrics
    
    def print_metrics(self, metrics: Dict[str, float]):
        """打印格式化的指标"""
        print("=" * 50)
        print("EVALUATION METRICS")
        print("=" * 50)
        
        print("\nSegmentation Performance:")
        print(f"  Dice Coefficient: {metrics['dice']:.4f}")
        print(f"  IoU Score:        {metrics['iou']:.4f}")
        print(f"  Sensitivity:      {metrics['sensitivity']:.4f}")
        print(f"  Specificity:      {metrics['specificity']:.4f}")
        print(f"  Precision:        {metrics['precision']:.4f}")
        print(f"  Hausdorff Dist:   {metrics['hausdorff']:.4f}")
        
        print("\nUncertainty Quantification:")
        print(f"  ECE:              {metrics['ece']:.4f}")
        print(f"  Uncertainty Corr: {metrics['uncertainty_correlation']:.4f}")
        
        print("\nReconstruction:")
        print(f"  Reconstruction Error: {metrics['reconstruction_error']:.4f}")
        
        if 'anomaly_auc' in metrics:
            print("\nAnomaly Detection:")
            print(f"  AUC:              {metrics['anomaly_auc']:.4f}")
            print(f"  Average Precision: {metrics['anomaly_ap']:.4f}")
        
        print("=" * 50)


# 使用示例
if __name__ == "__main__":
    # 创建模拟数据
    batch_size = 4
    height, width = 256, 256
    channels = 4
    
    seg_pred = torch.randn(batch_size, 1, height, width)
    seg_target = torch.randint(0, 2, (batch_size, 1, height, width)).float()
    uncertainty = torch.rand(batch_size, 1, height, width)
    reconstruction = torch.randn(batch_size, channels, height, width)
    original = torch.randn(batch_size, channels, height, width)
    is_healthy = torch.randint(0, 2, (batch_size,)).bool()
    
    # 计算指标
    evaluator = ComprehensiveMetrics()
    metrics = evaluator.compute_all_metrics(
        seg_pred, seg_target, uncertainty, reconstruction, original, is_healthy
    )
    
    # 打印结果
    evaluator.print_metrics(metrics)
    
    # 测试可靠性图
    uncertainty_metrics = UncertaintyMetrics()
    bin_conf, bin_acc, bin_counts = uncertainty_metrics.reliability_diagram(
        uncertainty, seg_pred, seg_target
    )
    
    print(f"\nReliability Diagram Data:")
    print(f"Bin Confidences: {bin_conf}")
    print(f"Bin Accuracies:  {bin_acc}")
    print(f"Bin Counts:      {bin_counts}")
