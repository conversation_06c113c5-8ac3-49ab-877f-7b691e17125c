"""
脑肿瘤分割模型
结合VAE和UNet的网络结构
"""

import torch
import torch.nn as nn
import torch.nn.functional as F


class ConvBlock(nn.Module):
    """
    基础卷积块
    使用GroupNorm和残差连接
    """
    
    def __init__(self, in_channels, out_channels, num_groups=8):
        super(ConvBlock, self).__init__()
        
        # 确保组数不超过通道数
        num_groups = min(num_groups, in_channels)
        
        self.norm1 = nn.GroupNorm(num_groups, in_channels)
        self.relu1 = nn.ReLU(inplace=True)
        self.conv1 = nn.Conv2d(in_channels, out_channels, kernel_size=3, padding=1)
        
        self.norm2 = nn.GroupNorm(min(num_groups, out_channels), out_channels)
        self.relu2 = nn.ReLU(inplace=True)
        self.conv2 = nn.Conv2d(out_channels, out_channels, kernel_size=3, padding=1)
        
        # 如果输入输出通道数不同，需要调整残差连接
        self.shortcut = nn.Identity()
        if in_channels != out_channels:
            self.shortcut = nn.Conv2d(in_channels, out_channels, kernel_size=1)
    
    def forward(self, x):
        residual = self.shortcut(x)
        
        out = self.relu1(self.norm1(x))
        out = self.conv1(out)
        
        out = self.relu2(self.norm2(out))
        out = self.conv2(out)
        
        out = out + residual
        return out


class VAEDecoder(nn.Module):
    """
    VAE解码器部分
    用于图像重构
    """
    
    def __init__(self, input_size, base_channels, output_channels, latent_channels=None):
        super(VAEDecoder, self).__init__()
        
        # 确保input_size是元组
        if isinstance(input_size, (list, tuple)):
            self.input_size = tuple(input_size)
        else:
            self.input_size = (input_size, input_size)
        
        if latent_channels:
            self.latent_channels = latent_channels
        else:
            self.latent_channels = base_channels * 4
        
        # 编码器：将特征图压缩到潜在空间
        self.feature_encoder = nn.Sequential(
            nn.GroupNorm(8, base_channels * 8),
            nn.ReLU(inplace=True),
            nn.Conv2d(base_channels * 8, self.latent_channels, 3, padding=1),
            nn.AdaptiveAvgPool2d(1)
        )
        
        # 潜在空间的均值和方差
        self.mean_layer = nn.Linear(self.latent_channels, self.latent_channels // 2)
        self.var_layer = nn.Linear(self.latent_channels, self.latent_channels // 2)
        
        # 解码器：从潜在空间重构
        h_small = self.input_size[0] // 16
        w_small = self.input_size[1] // 16
        recon_size = h_small * w_small
        
        self.feature_decoder = nn.Sequential(
            nn.Linear(self.latent_channels // 2, base_channels * 8 * recon_size),
            nn.ReLU(inplace=True)
        )
        
        # 保存重构形状信息
        self.h_small = h_small
        self.w_small = w_small
        self.recon_channels = base_channels * 8
        
        # 上采样层
        self.upsample4 = nn.Sequential(
            nn.Conv2d(base_channels * 8, base_channels * 8, 1),
            nn.Upsample(scale_factor=2, mode='bilinear', align_corners=False)
        )
        
        self.upsample3 = nn.Sequential(
            nn.Conv2d(base_channels * 8, base_channels * 4, 3, padding=1),
            nn.Upsample(scale_factor=2, mode='bilinear', align_corners=False),
            ConvBlock(base_channels * 4, base_channels * 4)
        )
        
        self.upsample2 = nn.Sequential(
            nn.Conv2d(base_channels * 4, base_channels * 2, 3, padding=1),
            nn.Upsample(scale_factor=2, mode='bilinear', align_corners=False),
            ConvBlock(base_channels * 2, base_channels * 2)
        )
        
        self.upsample1 = nn.Sequential(
            nn.Conv2d(base_channels * 2, base_channels, 3, padding=1),
            nn.Upsample(scale_factor=2, mode='bilinear', align_corners=False),
            ConvBlock(base_channels, base_channels)
        )
        
        self.output_conv = nn.Conv2d(base_channels, output_channels, 1)
    
    def reparameterize(self, mu, logvar):
        """重参数化技巧"""
        std = torch.exp(0.5 * logvar)
        eps = torch.randn_like(std)
        return eps.mul(std).add_(mu)
    
    def forward(self, x):
        # 编码
        x = self.feature_encoder(x)
        batch_size = x.size(0)
        x = x.view(batch_size, -1)
        
        # 潜在空间
        mu = self.mean_layer(x[:, :self.latent_channels])
        logvar = self.var_layer(x[:, :self.latent_channels])
        z = self.reparameterize(mu, logvar)
        
        # 解码
        decoded = self.feature_decoder(z)
        recon_shape = [batch_size, self.recon_channels, self.h_small, self.w_small]
        decoded = decoded.view(recon_shape)
        
        # 上采样重构
        x = self.upsample4(decoded)
        x = self.upsample3(x)
        x = self.upsample2(x)
        x = self.upsample1(x)
        output = self.output_conv(x)
        
        return output, mu, logvar


class UNetEncoder(nn.Module):
    """
    UNet编码器
    """
    
    def __init__(self, input_size, input_channels=4, output_channels=1, base_channels=32, dropout_rate=0.2):
        super(UNetEncoder, self).__init__()
        self.input_size = input_size
        self.input_channels = input_channels
        self.output_channels = output_channels
        self.base_channels = base_channels
        
        self.build_encoder()
        self.build_decoder()
        self.dropout = nn.Dropout2d(p=dropout_rate)
    
    def build_encoder(self):
        """构建编码器"""
        base_ch = self.base_channels
        
        # 第一层
        self.conv1a = nn.Conv2d(self.input_channels, base_ch, 3, padding=1)
        self.conv1b = ConvBlock(base_ch, base_ch)
        
        # 下采样
        self.downsample1 = nn.Conv2d(base_ch, base_ch * 2, 3, stride=2, padding=1)
        
        # 第二层
        self.conv2a = ConvBlock(base_ch * 2, base_ch * 2)
        self.conv2b = ConvBlock(base_ch * 2, base_ch * 2)
        
        self.downsample2 = nn.Conv2d(base_ch * 2, base_ch * 4, 3, stride=2, padding=1)
        
        # 第三层
        self.conv3a = ConvBlock(base_ch * 4, base_ch * 4)
        self.conv3b = ConvBlock(base_ch * 4, base_ch * 4)
        
        self.downsample3 = nn.Conv2d(base_ch * 4, base_ch * 8, 3, stride=2, padding=1)
        
        # 第四层（瓶颈层）
        self.conv4a = ConvBlock(base_ch * 8, base_ch * 8)
        self.conv4b = ConvBlock(base_ch * 8, base_ch * 8)
        self.conv4c = ConvBlock(base_ch * 8, base_ch * 8)
        self.conv4d = ConvBlock(base_ch * 8, base_ch * 8)
    
    def build_decoder(self):
        """构建解码器"""
        base_ch = self.base_channels
        
        # 上采样层
        self.upsample4a = nn.Conv2d(base_ch * 8, base_ch * 4, 1)
        self.upsample4_up = nn.Upsample(scale_factor=2, mode='bilinear', align_corners=False)
        self.upsample4b = ConvBlock(base_ch * 4, base_ch * 4)
        
        self.upsample3a = nn.Conv2d(base_ch * 4, base_ch * 2, 1)
        self.upsample3_up = nn.Upsample(scale_factor=2, mode='bilinear', align_corners=False)
        self.upsample3b = ConvBlock(base_ch * 2, base_ch * 2)
        
        self.upsample2a = nn.Conv2d(base_ch * 2, base_ch, 1)
        self.upsample2_up = nn.Upsample(scale_factor=2, mode='bilinear', align_corners=False)
        self.upsample2b = ConvBlock(base_ch, base_ch)
        
        # 最终输出层
        self.output_conv = nn.Conv2d(base_ch, self.output_channels, 1)
    
    def forward(self, x):
        # 编码器
        c1 = self.conv1a(x)
        c1 = self.conv1b(c1)
        c1d = self.downsample1(c1)
        
        c2 = self.conv2a(c1d)
        c2 = self.conv2b(c2)
        c2d = self.downsample2(c2)
        
        c3 = self.conv3a(c2d)
        c3 = self.conv3b(c3)
        c3d = self.downsample3(c3)
        
        c4 = self.conv4a(c3d)
        c4 = self.conv4b(c4)
        c4 = self.conv4c(c4)
        c4d = self.conv4d(c4)
        
        # 应用dropout
        c4d = self.dropout(c4d)
        
        # 解码器（带跳跃连接）
        u4 = self.upsample4a(c4d)
        u4 = self.upsample4_up(u4)
        u4 = u4 + c3  # 跳跃连接
        u4 = self.upsample4b(u4)
        
        u3 = self.upsample3a(u4)
        u3 = self.upsample3_up(u3)
        u3 = u3 + c2  # 跳跃连接
        u3 = self.upsample3b(u3)
        
        u2 = self.upsample2a(u3)
        u2 = self.upsample2_up(u2)
        u2 = u2 + c1  # 跳跃连接
        u2 = self.upsample2b(u2)
        
        # 输出
        output = self.output_conv(u2)
        
        return output, c4d


class SegmentationModel(nn.Module):
    """
    脑肿瘤分割模型
    结合UNet和VAE
    """
    
    def __init__(self, input_shape=(240, 240), input_channels=4, output_channels=1, 
                 base_channels=32, latent_dim=128, dropout_rate=0.2):
        super(SegmentationModel, self).__init__()
        
        self.input_shape = input_shape
        self.latent_dim = latent_dim
        
        # 主UNet网络
        self.unet = UNetEncoder(input_shape, input_channels, output_channels, base_channels, dropout_rate)
        
        # VAE分支（用于重构）
        self.vae_decoder = VAEDecoder(input_shape, base_channels, input_channels)
        
        # 不确定性估计分支
        self.uncertainty_estimator = nn.Sequential(
            nn.Conv2d(base_channels * 8, base_channels * 4, 3, padding=1),
            nn.GroupNorm(8, base_channels * 4),
            nn.ReLU(inplace=True),
            nn.Conv2d(base_channels * 4, base_channels * 2, 3, padding=1),
            nn.GroupNorm(8, base_channels * 2),
            nn.ReLU(inplace=True),
            nn.Conv2d(base_channels * 2, 1, 1),
            nn.Sigmoid()  # 输出0-1之间的不确定性
        )
    
    def forward(self, x):
        # 主分割网络
        segmentation_output, bottleneck_features = self.unet(x)
        
        # VAE分支（重构）
        reconstruction, mu, logvar = self.vae_decoder(bottleneck_features)
        
        # 不确定性估计
        uncertainty = self.uncertainty_estimator(bottleneck_features)
        
        # 上采样不确定性到原始尺寸
        uncertainty = F.interpolate(uncertainty, size=x.shape[2:], 
                                   mode='bilinear', align_corners=False)
        
        return segmentation_output, reconstruction, mu, logvar, uncertainty


# 测试代码
if __name__ == "__main__":
    # 创建模型
    model = SegmentationModel(
        input_shape=(240, 240),
        input_channels=4,
        output_channels=1,
        base_channels=32,
        latent_dim=128,
        dropout_rate=0.2
    )
    
    # 测试输入
    batch_size = 2
    x = torch.randn(batch_size, 4, 240, 240)
    
    # 前向传播
    with torch.no_grad():
        seg_out, reconstruction, mu, logvar, uncertainty = model(x)
    
    print("脑肿瘤分割模型测试:")
    print(f"输入形状: {x.shape}")
    print(f"分割输出: {seg_out.shape}")
    print(f"重构输出: {reconstruction.shape}")
    print(f"均值形状: {mu.shape}")
    print(f"方差形状: {logvar.shape}")
    print(f"不确定性形状: {uncertainty.shape}")
    
    # 计算参数数量
    total_params = sum(p.numel() for p in model.parameters())
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    
    print(f"\n模型参数:")
    print(f"总参数数: {total_params:,}")
    print(f"可训练参数: {trainable_params:,}")
