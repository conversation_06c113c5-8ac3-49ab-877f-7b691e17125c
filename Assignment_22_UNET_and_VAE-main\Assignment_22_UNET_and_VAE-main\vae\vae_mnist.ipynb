{"nbformat": 4, "nbformat_minor": 0, "metadata": {"colab": {"provenance": [], "gpuType": "T4"}, "kernelspec": {"name": "python3", "display_name": "Python 3"}, "language_info": {"name": "python"}, "accelerator": "GPU", "widgets": {"application/vnd.jupyter.widget-state+json": {"1e1c0425c35547a39becb1bb9336d403": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_2b953635c78a453092abd6ab29210365", "IPY_MODEL_f52a3d748def42a2a41994efc5b07ba7", "IPY_MODEL_1f38d48bccb7440f8b8c667f2e1f94f6"], "layout": "IPY_MODEL_bdb3a112905741edab1cfe0b5cd79499"}}, "2b953635c78a453092abd6ab29210365": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_6ed889b12b434455bcb9f355a81cd130", "placeholder": "​", "style": "IPY_MODEL_a09b2f72027a4226abf2d1fa41869a03", "value": "Epoch 9: 100%"}}, "f52a3d748def42a2a41994efc5b07ba7": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_aec5ae939fd3455bbd852718dac6cae0", "max": 1875, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_e76d104e699149da8a17029544d75d13", "value": 1875}}, "1f38d48bccb7440f8b8c667f2e1f94f6": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_98c152b20bfd49438526d7bdd0665c6f", "placeholder": "​", "style": "IPY_MODEL_d94ea2af75e84a888c173aa85685b88d", "value": " 1875/1875 [01:44&lt;00:00, 17.93it/s, loss=870, v_num=0]"}}, "bdb3a112905741edab1cfe0b5cd79499": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": "inline-flex", "flex": null, "flex_flow": "row wrap", "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": "100%"}}, "6ed889b12b434455bcb9f355a81cd130": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "a09b2f72027a4226abf2d1fa41869a03": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "aec5ae939fd3455bbd852718dac6cae0": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": "2", "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "e76d104e699149da8a17029544d75d13": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "98c152b20bfd49438526d7bdd0665c6f": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "d94ea2af75e84a888c173aa85685b88d": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}}}}, "cells": [{"cell_type": "code", "execution_count": 1, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "q22kQ0_tJjeS", "outputId": "8ac21ada-761d-4ca0-af49-beb428a8138d"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m812.3/812.3 kB\u001b[0m \u001b[31m9.1 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m868.8/868.8 kB\u001b[0m \u001b[31m17.6 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m21.3/21.3 MB\u001b[0m \u001b[31m45.1 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m300.8/300.8 kB\u001b[0m \u001b[31m7.1 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m829.5/829.5 kB\u001b[0m \u001b[31m14.2 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25h"]}], "source": ["! pip install pytorch-lightning --quiet\n", "! pip install lightning-bolts --quiet"]}, {"cell_type": "code", "source": ["import torch\n", "from torch import nn\n", "from torch.nn import functional as F\n", "from torchvision import transforms\n", "from torchvision.datasets import MNIST\n", "from torch.utils.data import DataLoader\n", "import pytorch_lightning as pl\n", "from pytorch_lightning.callbacks import ModelSummary"], "metadata": {"id": "EDmkxJ9QKD-2"}, "execution_count": 2, "outputs": []}, {"cell_type": "code", "source": ["BATCH_SIZE = 32\n", "\n", "normalize_data = transforms.Compose([transforms.ToTensor(),\n", "                                     transforms.Normalize((0.1307,), (0.3081,))\n", "                                     ])\n", "\n", "train_dataset = MNIST(root='./data', train=True, transform=normalize_data, download=True)\n", "train_dataloader = DataLoader(dataset=train_dataset, batch_size=BATCH_SIZE, shuffle=True)\n", "\n", "test_dataset = MNIST(root='./data', train=False, transform=normalize_data, download=True)\n", "test_dataloader = DataLoader(dataset = test_dataset, batch_size=BATCH_SIZE, shuffle=False)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "KhaVjiQcKJGP", "outputId": "19dcfafc-4f3b-4bb9-892b-e2dcab5c9d5e"}, "execution_count": 4, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Downloading http://yann.lecun.com/exdb/mnist/train-images-idx3-ubyte.gz\n", "Failed to download (trying next):\n", "HTTP Error 403: Forbidden\n", "\n", "Downloading https://ossci-datasets.s3.amazonaws.com/mnist/train-images-idx3-ubyte.gz\n", "Downloading https://ossci-datasets.s3.amazonaws.com/mnist/train-images-idx3-ubyte.gz to ./data/MNIST/raw/train-images-idx3-ubyte.gz\n"]}, {"output_type": "stream", "name": "stderr", "text": ["100%|██████████| 9912422/9912422 [00:00<00:00, 11837805.69it/s]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["Extracting ./data/MNIST/raw/train-images-idx3-ubyte.gz to ./data/MNIST/raw\n", "\n", "Downloading http://yann.lecun.com/exdb/mnist/train-labels-idx1-ubyte.gz\n", "Failed to download (trying next):\n", "HTTP Error 403: Forbidden\n", "\n", "Downloading https://ossci-datasets.s3.amazonaws.com/mnist/train-labels-idx1-ubyte.gz\n", "Downloading https://ossci-datasets.s3.amazonaws.com/mnist/train-labels-idx1-ubyte.gz to ./data/MNIST/raw/train-labels-idx1-ubyte.gz\n"]}, {"output_type": "stream", "name": "stderr", "text": ["100%|██████████| 28881/28881 [00:00<00:00, 366605.72it/s]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["Extracting ./data/MNIST/raw/train-labels-idx1-ubyte.gz to ./data/MNIST/raw\n", "\n", "Downloading http://yann.lecun.com/exdb/mnist/t10k-images-idx3-ubyte.gz\n", "Failed to download (trying next):\n", "HTTP Error 403: Forbidden\n", "\n", "Downloading https://ossci-datasets.s3.amazonaws.com/mnist/t10k-images-idx3-ubyte.gz\n", "Downloading https://ossci-datasets.s3.amazonaws.com/mnist/t10k-images-idx3-ubyte.gz to ./data/MNIST/raw/t10k-images-idx3-ubyte.gz\n"]}, {"output_type": "stream", "name": "stderr", "text": ["100%|██████████| 1648877/1648877 [00:00<00:00, 3182677.51it/s]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["Extracting ./data/MNIST/raw/t10k-images-idx3-ubyte.gz to ./data/MNIST/raw\n", "\n", "Downloading http://yann.lecun.com/exdb/mnist/t10k-labels-idx1-ubyte.gz\n", "Failed to download (trying next):\n", "HTTP Error 403: Forbidden\n", "\n", "Downloading https://ossci-datasets.s3.amazonaws.com/mnist/t10k-labels-idx1-ubyte.gz\n", "Downloading https://ossci-datasets.s3.amazonaws.com/mnist/t10k-labels-idx1-ubyte.gz to ./data/MNIST/raw/t10k-labels-idx1-ubyte.gz\n"]}, {"output_type": "stream", "name": "stderr", "text": ["100%|██████████| 4542/4542 [00:00<00:00, 9835069.06it/s]"]}, {"output_type": "stream", "name": "stdout", "text": ["Extracting ./data/MNIST/raw/t10k-labels-idx1-ubyte.gz to ./data/MNIST/raw\n", "\n"]}, {"output_type": "stream", "name": "stderr", "text": ["\n"]}]}, {"cell_type": "code", "source": ["next(iter(train_dataloader))[0].shape"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "7vevaa55KQIl", "outputId": "30b4f0de-9f72-4c03-883e-8e66f4560e15"}, "execution_count": 5, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["torch.<PERSON><PERSON>([32, 1, 28, 28])"]}, "metadata": {}, "execution_count": 5}]}, {"cell_type": "code", "source": ["next(iter(train_dataloader))[1].shape"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "GgDVLIhiKRcr", "outputId": "2b4508cb-2dad-4a84-9a25-2262248dd54c"}, "execution_count": 6, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["<PERSON>.<PERSON><PERSON>([32])"]}, "metadata": {}, "execution_count": 6}]}, {"cell_type": "code", "source": ["class VAE(pl.LightningModule):\n", "    \"\"\"\n", "    Lightning Module for VAE\n", "    \"\"\"\n", "    def __init__(self, enc_out_dim=256, latent_dim=256, input_height=28):\n", "        \"\"\"\n", "        <PERSON><PERSON><PERSON><PERSON>\n", "        \"\"\"\n", "        # Initialize the lightning module\n", "        super().__init__()\n", "\n", "        # Save the input params\n", "        self.save_hyperparameters()\n", "\n", "        # Encoder Layers\n", "        self.encoder_conv_layers = nn.Sequential(\n", "            self.conv_layer(in_channels=2, out_channels=32),             # Input Channels = 4  [2 for Grayscale and 1 for label data]\n", "            self.conv_layer(in_channels=32, out_channels=64),\n", "            self.conv_layer(in_channels=64, out_channels=128),\n", "            self.conv_layer(in_channels=128, out_channels=256)\n", "        )\n", "\n", "        # distribution parameters\n", "        self.fc_mu = nn.Linear(enc_out_dim * 20 * 20, latent_dim)        # 20 since input image size is reduced to 20 after 3 conv layers\n", "        self.fc_var = nn.Linear(enc_out_dim * 20 * 20, latent_dim)\n", "\n", "        # Decoder's <PERSON>ar Layer\n", "        self.linear = nn.Linear(latent_dim + 10, enc_out_dim * 20 * 20)  # 10 [One for each class]\n", "\n", "        # Decoder Layers\n", "        self.decoder_conv_layers = nn.Sequential(\n", "            self.transpose_layer(in_channels=256, out_channels=128),\n", "            nn.ReLU(),\n", "            self.transpose_layer(in_channels=128, out_channels=64),\n", "            nn.ReLU(),\n", "            self.transpose_layer(in_channels=64, out_channels=32),\n", "            nn.ReLU(),\n", "            self.transpose_layer(in_channels=32, out_channels=1)\n", "        )\n", "\n", "        # for the gaussian likelihood\n", "        self.log_scale = nn.Parameter(torch.Tensor([0.0]))\n", "\n", "    def conv_layer(self, in_channels, out_channels):\n", "        \"\"\"\n", "        Function to return conv layer\n", "        \"\"\"\n", "        return nn.Sequential(\n", "            nn.Conv2d(in_channels=in_channels, out_channels=out_channels, kernel_size=3, bias=False),\n", "            nn.ReLU()\n", "        )\n", "\n", "    def transpose_layer(self, in_channels, out_channels):\n", "        \"\"\"\n", "        Function to return Transpose layer\n", "        \"\"\"\n", "        return nn.Sequential(\n", "            nn.ConvTranspose2d(in_channels, out_channels, kernel_size=3, bias=False)\n", "        )\n", "\n", "    def encoder(self, x, y):\n", "        \"\"\"\n", "        Encoder Block of the VAE\n", "        \"\"\"\n", "        # Batch size and input dimensions\n", "        batch_size = x.shape[0]\n", "        img_height = x.shape[2]   # Height = Width\n", "\n", "        # Concatenate Label data to the image data\n", "        y = torch.argmax(y, dim=1).reshape((y.shape[0], 1, 1, 1))\n", "        y = torch.ones(x.shape).to(self.device) * y\n", "        concat_input = torch.cat((x, y), dim=1)\n", "\n", "        # Pass the concatenated input through the encoder layers and flatten it\n", "        x = self.encoder_conv_layers(concat_input)\n", "        x = x.view(batch_size, -1)\n", "        return x\n", "\n", "    def decoder(self, z, y):\n", "        \"\"\"\n", "        Decoder Block of VAE\n", "        \"\"\"\n", "        # Add 10 neurons (one for each class to the latent layer)\n", "        z = torch.cat((z, y.float()), dim=1)\n", "\n", "        # Latent layer\n", "        x_hat = <PERSON>.relu(self.linear(z))\n", "        x_hat = x_hat.reshape(-1, 256, 20, 20)\n", "\n", "        # Decoder layers\n", "        x_hat = self.decoder_conv_layers(x_hat)\n", "        x_hat = torch.sigmoid(x_hat)\n", "        return x_hat\n", "\n", "    def configure_optimizers(self):\n", "        \"\"\"\n", "        Optimizer for model training\n", "        \"\"\"\n", "        return torch.optim.<PERSON>(self.parameters(), lr=1e-4)\n", "\n", "    def gaussian_likelihood(self, mean, logscale, sample):\n", "        \"\"\"\n", "        \"\"\"\n", "        scale = torch.exp(logscale)\n", "        dist = torch.distributions.Normal(mean, scale)\n", "        log_pxz = dist.log_prob(sample)\n", "        return log_pxz.sum(dim=(1, 2, 3))\n", "\n", "    def kl_divergence(self, z, mu, std):\n", "        \"\"\"\n", "        \"\"\"\n", "        # --------------------------\n", "        # Monte carlo KL divergence\n", "        # --------------------------\n", "        # 1. define the first two probabilities (in this case Normal for both)\n", "        p = torch.distributions.Normal(torch.zeros_like(mu), torch.ones_like(std))\n", "        q = torch.distributions.Normal(mu, std)\n", "\n", "        # 2. get the probabilities from the equation\n", "        log_qzx = q.log_prob(z)\n", "        log_pz = p.log_prob(z)\n", "\n", "        # kl\n", "        kl = (log_qzx - log_pz)\n", "        kl = kl.sum(-1)\n", "        return kl\n", "\n", "    def forward(self, x, y):\n", "        \"\"\"\n", "        Forward method\n", "        \"\"\"\n", "        # encode x to get the mu and variance parameters\n", "        x_encoded = self.encoder(x, y)\n", "\n", "        mu, log_var = self.fc_mu(x_encoded), self.fc_var(x_encoded)\n", "        std = torch.exp(log_var/2)\n", "        q = torch.distributions.Normal(mu,std)\n", "        z = q.rsample()\n", "\n", "        x_hat = self.decoder(z, y)\n", "        return x_hat\n", "\n", "    def training_step(self, batch, batch_idx):\n", "        \"\"\"\n", "        Function to train the model\n", "        \"\"\"\n", "        # Input data\n", "        x, y = batch\n", "\n", "        # One-Hot encoding of label data\n", "        y = F.one_hot(y, num_classes=10)\n", "\n", "        # encode x to get the mu and variance parameters\n", "        x_encoded = self.encoder(x, y)\n", "\n", "        # Encoder output mu and sigma\n", "        mu, log_var = self.fc_mu(x_encoded), self.fc_var(x_encoded)\n", "\n", "        # sample z from q\n", "        std = torch.exp(log_var / 2)\n", "        q = torch.distributions.Normal(mu, std)\n", "        z = q.rsample()\n", "\n", "        # decoded\n", "        x_hat = self.decoder(z, y)\n", "\n", "        # reconstruction loss\n", "        recon_loss = self.gaussian_likelihood(x_hat, self.log_scale, x)\n", "\n", "        # kl\n", "        kl = self.kl_divergence(z, mu, std)\n", "\n", "        # elbo\n", "        elbo = (kl - recon_loss)\n", "        elbo = elbo.mean()\n", "\n", "        self.log_dict({\n", "            'elbo': elbo,\n", "            'kl': kl.mean(),\n", "            'recon_loss': recon_loss.mean(),\n", "            'reconstruction': recon_loss.mean(),\n", "            'kl': kl.mean(),\n", "        })\n", "\n", "        return elbo"], "metadata": {"id": "02ztiYeKKed-"}, "execution_count": 7, "outputs": []}, {"cell_type": "code", "source": ["# Seed for deterministic results\n", "pl.seed_everything(8)\n", "\n", "# Instance of the model\n", "model = VAE()\n", "\n", "# Trainer configuration\n", "trainer = pl.Trainer(\n", "    callbacks=[ModelSummary(max_depth=1)],\n", "    gpus=1,\n", "    num_sanity_val_steps=1,\n", "    max_epochs=10\n", "    )\n", "\n", "# Train the model\n", "trainer.fit(model, train_dataloader, test_dataloader)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 541, "referenced_widgets": ["1e1c0425c35547a39becb1bb9336d403", "2b953635c78a453092abd6ab29210365", "f52a3d748def42a2a41994efc5b07ba7", "1f38d48bccb7440f8b8c667f2e1f94f6", "bdb3a112905741edab1cfe0b5cd79499", "6ed889b12b434455bcb9f355a81cd130", "a09b2f72027a4226abf2d1fa41869a03", "aec5ae939fd3455bbd852718dac6cae0", "e76d104e699149da8a17029544d75d13", "98c152b20bfd49438526d7bdd0665c6f", "d94ea2af75e84a888c173aa85685b88d"]}, "id": "IqpNIqbmKlzP", "outputId": "71aa135d-9662-4c9c-c72b-8c68db39e0ce"}, "execution_count": 8, "outputs": [{"output_type": "stream", "name": "stderr", "text": ["INFO:lightning_fabric.utilities.seed:Global seed set to 8\n", "/usr/local/lib/python3.10/dist-packages/pytorch_lightning/trainer/connectors/accelerator_connector.py:478: LightningDeprecationWarning: Setting `Trainer(gpus=1)` is deprecated in v1.7 and will be removed in v2.0. Please use `Trainer(accelerator='gpu', devices=1)` instead.\n", "  rank_zero_deprecation(\n", "INFO:pytorch_lightning.utilities.rank_zero:Trainer already configured with model summary callbacks: [<class 'pytorch_lightning.callbacks.model_summary.ModelSummary'>]. Skipping setting a default `ModelSummary` callback.\n", "INFO:pytorch_lightning.utilities.rank_zero:GPU available: True (cuda), used: True\n", "INFO:pytorch_lightning.utilities.rank_zero:TPU available: False, using: 0 TPU cores\n", "INFO:pytorch_lightning.utilities.rank_zero:IPU available: False, using: 0 IPUs\n", "INFO:pytorch_lightning.utilities.rank_zero:HPU available: False, using: 0 HPUs\n", "/usr/local/lib/python3.10/dist-packages/pytorch_lightning/trainer/configuration_validator.py:106: UserWarning: You passed in a `val_dataloader` but have no `validation_step`. Skipping val loop.\n", "  rank_zero_warn(\"You passed in a `val_dataloader` but have no `validation_step`. Skipping val loop.\")\n", "WARNING:pytorch_lightning.loggers.tensorboard:Missing logger folder: /content/lightning_logs\n", "INFO:pytorch_lightning.accelerators.cuda:LOCAL_RANK: 0 - CUDA_VISIBLE_DEVICES: [0]\n", "INFO:pytorch_lightning.callbacks.model_summary:\n", "  | Name                | Type       | Params\n", "---------------------------------------------------\n", "0 | encoder_conv_layers | Sequential | 387 K \n", "1 | fc_mu               | Linear     | 26.2 M\n", "2 | fc_var              | Linear     | 26.2 M\n", "3 | linear              | Linear     | 27.3 M\n", "4 | decoder_conv_layers | Sequential | 387 K \n", "---------------------------------------------------\n", "80.5 M    Trainable params\n", "0         Non-trainable params\n", "80.5 M    Total params\n", "322.180   Total estimated model params size (MB)\n"]}, {"output_type": "display_data", "data": {"text/plain": ["Training: 0it [00:00, ?it/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "1e1c0425c35547a39becb1bb9336d403"}}, "metadata": {}}, {"output_type": "stream", "name": "stderr", "text": ["INFO:pytorch_lightning.utilities.rank_zero:`Trainer.fit` stopped: `max_epochs=10` reached.\n"]}]}, {"cell_type": "code", "source": ["torch.save(model.state_dict(), 'vae_model_mnist.pth')"], "metadata": {"id": "alrp9xoYLDB5"}, "execution_count": 10, "outputs": []}, {"cell_type": "code", "source": ["import matplotlib.pyplot as plt\n", "from matplotlib.pyplot import imshow, figure\n", "import numpy as np\n", "\n", "\n", "def vae_results(x, y, model, classes, correct_labels=True, num_outputs=25):\n", "    \"\"\"\n", "    Function to display output images generated by the model\n", "    \"\"\"\n", "    # Figure to display the results\n", "    figure(figsize=(8, 3), dpi=300)\n", "    device = model.device\n", "\n", "    # Set the model to eval mode\n", "    with torch.no_grad():\n", "        # Change the value to get incorrect label values\n", "        y_incorrect = y - 1                   # Reduce class value by 1\n", "        y_incorrect[y_incorrect == -1] = 9    # Change -1 to 9\n", "\n", "        # One-Hot Encoding\n", "        one_hot_y = F.one_hot(y, num_classes=10)\n", "        one_hot_incorrect_y = F.one_hot(y_incorrect, num_classes=10)\n", "\n", "        # Send correct labels to Encoder based on function argument\n", "        if correct_labels:\n", "            x_encoded = model.encoder(x.to(device), one_hot_y.to(device))\n", "        else:\n", "            x_encoded = model.encoder(x.to(device), one_hot_incorrect_y.to(device))\n", "\n", "        # Get mean and variance from encoder output\n", "        mu, log_var = model.fc_mu(x_encoded), model.fc_var(x_encoded)\n", "\n", "        # Calculate standard deviation\n", "        std = torch.exp(log_var/2)\n", "        q = torch.distributions.Normal(mu,std)\n", "        z = q.rsample()\n", "\n", "        # Send correct labels to Decoder based on function argument\n", "        if correct_labels:\n", "            x_hat = model.decoder(z, one_hot_y.to(device))\n", "        else:\n", "            x_hat = model.decoder(z, one_hot_incorrect_y.to(device))\n", "\n", "        # Plot Results\n", "        fig = plt.figure(figsize=(10, 10))\n", "        for index in np.arange(num_outputs):\n", "            axs = fig.add_subplot(5, 5, index + 1, xticks=[], yticks=[])\n", "            img = x_hat[index].to('cpu')\n", "            plt.imshow(img.permute(1, 2, 0), cmap='gray')\n", "\n", "            if correct_labels:\n", "                axs.set_title(f\"Label: {classes[y[index]]}\")\n", "            else:\n", "                axs.set_title(f\"Incorrect Label: {classes[y_incorrect[index]]}\\n Correct Label: {classes[y[index]]}\")\n", "\n", "        fig.tight_layout()\n", "        plt.show()"], "metadata": {"id": "0aFAu7MEKrrZ"}, "execution_count": 9, "outputs": []}, {"cell_type": "code", "source": ["classes = list(range(0, 10))\n", "x, y = next(iter(test_dataloader))\n", "vae_results(x, y, model, classes)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 1000}, "id": "jsF4iBVZKxAU", "outputId": "f31b51d4-d0a5-490b-9ca8-f9979c57430d"}, "execution_count": 11, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["<Figure size 2400x900 with 0 Axes>"]}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["<Figure size 1000x1000 with 25 Axes>"], "image/png": "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\n"}, "metadata": {}}]}, {"cell_type": "code", "source": ["vae_results(x, y, model, classes, correct_labels=False)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 1000}, "id": "feIc6FDvKyHj", "outputId": "9499b211-b084-48ff-8c50-37f27a6c8a99"}, "execution_count": 12, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["<Figure size 2400x900 with 0 Axes>"]}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["<Figure size 1000x1000 with 25 Axes>"], "image/png": "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\n"}, "metadata": {}}]}, {"cell_type": "code", "source": ["vae_results(x, y, model, classes, correct_labels=True)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 1000}, "id": "Fa4a9J9_K_F9", "outputId": "a3cf1d9f-dd1d-487d-95ba-e69b65c0de64"}, "execution_count": 13, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["<Figure size 2400x900 with 0 Axes>"]}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["<Figure size 1000x1000 with 25 Axes>"], "image/png": "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\n"}, "metadata": {}}]}]}