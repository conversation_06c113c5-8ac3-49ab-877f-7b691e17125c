# VAE-UNet Brain Tumor Segmentation Requirements
# 脑肿瘤MRI分割和不确定性分析项目依赖

# Deep Learning Framework
torch>=1.12.0
torchvision>=0.13.0
torchaudio>=0.12.0

# Medical Image Processing
nibabel>=4.0.0
SimpleITK>=2.2.0
monai>=1.0.0

# Data Processing and Augmentation
numpy>=1.21.0
scipy>=1.9.0
scikit-image>=0.19.0
opencv-python>=4.6.0
albumentations>=1.3.0
Pillow>=9.0.0

# Machine Learning and Metrics
scikit-learn>=1.1.0
pandas>=1.4.0

# Visualization
matplotlib>=3.5.0
seaborn>=0.11.0
plotly>=5.10.0

# Progress Bars and Logging
tqdm>=4.64.0

# Experiment Tracking
tensorboard>=2.10.0
wandb>=0.13.0

# Configuration and Utilities
pyyaml>=6.0
argparse
pathlib
json5

# Jupyter Notebook (optional)
jupyter>=1.0.0
ipywidgets>=7.7.0

# Development Tools (optional)
pytest>=7.0.0
black>=22.0.0
flake8>=5.0.0

# GPU Acceleration (optional, install based on your CUDA version)
# For CUDA 11.6:
# torch==1.12.1+cu116
# torchvision==0.13.1+cu116
# torchaudio==0.12.1+cu116

# For CUDA 11.7:
# torch==1.13.1+cu117
# torchvision==0.14.1+cu117
# torchaudio==0.13.1+cu117

# For CPU only:
# torch==1.13.1+cpu
# torchvision==0.14.1+cpu
# torchaudio==0.13.1+cpu
