
<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8">
    <script type="module">
      import mermaid from 'https://cdn.jsdelivr.net/npm/mermaid@10/dist/mermaid.esm.min.mjs';
      mermaid.initialize({ startOnLoad: true });
    </script>
  </head>
  <body>
    <div class="mermaid">
      graph TD
    A[Input: 4modalitiesMRI<br/>B×4×240×240] --> B[Conv1a: 4→32<br/>B×32×240×240]
    B --> C[ConvBlock1b<br/>B×32×240×240]
    C --> D[Downsample1<br/>B×64×120×120]
    D --> E[ConvBlock2a,2b<br/>B×64×120×120]
    E --> F[Downsample2<br/>B×128×60×60]
    F --> G[ConvBlock3a,3b<br/>B×128×60×60]
    G --> H[Downsample3<br/>B×256×30×30]
    H --> I[Bottleneck: ConvBlock4a,4b,4c,4d<br/>B×256×30×30]
    
    %% FeatureShared点
    I --> J[Segmentation Decoder Branch]
    I --> K[VAEDecoder Branch]
    I --> L[Uncertainty Estimation Branch]
    
    %% 分割分支
    J --> J1[Upsample4 + Skip Connection<br/>B×128×60×60]
    J1 --> J2[Upsample3 + Skip Connection<br/>B×64×120×120]
    J2 --> J3[Upsample2 + Skip Connection<br/>B×32×240×240]
    J3 --> J4[Output Conv<br/>B×1×240×240]
    
    %% VAE分支
    K --> K1[Feature Encoder<br/>AdaptiveAvgPool2d<br/>B×256×1×1]
    K1 --> K2[Flatten<br/>B×256]
    K2 --> K3[Mean Layer<br/>μ: B×128]
    K2 --> K4[Var Layer<br/>log σ²: B×128]
    K3 --> K5[Reparameterize<br/>z = μ + ε×σ<br/>B×128]
    K4 --> K5
    K5 --> K6[Feature Decoder<br/>B×256×15×15]
    K6 --> K7[4 layers Upsample<br/>B×4×240×240]
    
    %% 不确定性分支
    L --> L1[Conv: 256→128<br/>B×128×30×30]
    L1 --> L2[Conv: 128→64<br/>B×64×30×30]
    L2 --> L3[Conv: 64→1<br/>B×1×30×30]
    L3 --> L4[Interpolate<br/>B×1×240×240]
    
    %% 输出
    J4 --> O1[Mask for segmentation<br/>B×1×240×240]
    K7 --> O2[Reconstructed Image<br/>B×4×240×240]
    L4 --> O3[Uncertainty heatmap<br/>B×1×240×240]
    K3 --> O4[VAEMean μ<br/>B×128]
    K4 --> O5[VAEVariance log σ²<br/>B×128]
    
    %% 跳跃连接
    C -.->|Skip Connection| J3
    E -.->|Skip Connection| J2
    G -.->|Skip Connection| J1
    
    %% 样式
    classDef input fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef encoder fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef bottleneck fill:#fff3e0,stroke:#e65100,stroke-width:3px
    classDef branch fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px
    classDef output fill:#fce4ec,stroke:#880e4f,stroke-width:2px
    
    class A input
    class B,C,D,E,F,G,H encoder
    class I bottleneck
    class J,K,L,J1,J2,J3,K1,K2,K3,K4,K5,K6,L1,L2,L3 branch
    class J4,K7,L4,O1,O2,O3,O4,O5 output

    </div>
  </body>
</html>
