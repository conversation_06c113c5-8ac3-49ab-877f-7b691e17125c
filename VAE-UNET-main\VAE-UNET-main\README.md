# VAE-UNet for Diabetic Retinopathy Segmentation

A deep learning model combining Variational Autoencoders (VAE) and U-Net architectures for segmentation of diabetic
retinopathy lesions in fundus images. This model incorporates uncertainty quantification to improve detection
reliability.

## Requirements

## Installation

```bash
git clone https://github.com/tmuird/VAE-UNET.git
cd VAE-UNET
pip install -r requirements.txt
```


