{"nbformat": 4, "nbformat_minor": 0, "metadata": {"colab": {"name": "UNet_VAE_MNIST.ipynb", "provenance": [], "collapsed_sections": ["q60Uk4F2Gz7N"], "authorship_tag": "ABX9TyMOkuO8Fk9dvglrFLPP3y7Z", "include_colab_link": true}, "kernelspec": {"name": "python3", "display_name": "Python 3"}, "accelerator": "GPU"}, "cells": [{"cell_type": "markdown", "metadata": {"id": "view-in-github", "colab_type": "text"}, "source": ["<a href=\"https://colab.research.google.com/github/YassineNJ/UNet-implementation/blob/main/UNet_VAE_MNIST.ipynb\" target=\"_parent\"><img src=\"https://colab.research.google.com/assets/colab-badge.svg\" alt=\"Open In Colab\"/></a>"]}, {"cell_type": "code", "metadata": {"id": "Zih-1G0iDf94"}, "source": ["import torch\n", "import numpy as np\n", "import torch.nn as nn\n", "import torch.nn.functional as F\n", "import torchvision\n", "import torch.optim as optim\n", "import matplotlib\n", "import matplotlib.pyplot as plt\n", "import torchvision.transforms as transforms\n", "from tqdm import tqdm\n", "from torchvision import datasets\n", "from torch.utils.data import DataLoader\n", "from google.colab import drive\n", "drive.mount('/content/drive')"], "execution_count": null, "outputs": []}, {"cell_type": "code", "metadata": {"id": "4YdyrcvzTYY3"}, "source": ["batch_size = 64\n", "device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')\n", "train_data = datasets.MNIST(root='../input/data',train=True,download=True,transform=transforms.ToTensor())\n", "train_loader = DataLoader(train_data,batch_size=batch_size,shuffle=True)\n", "val_data = datasets.MNIST(root='../input/data',train=False,download=True,transform=transforms.ToTensor())\n", "val_loader = DataLoader(val_data,batch_size=batch_size,shuffle=False)"], "execution_count": null, "outputs": []}, {"cell_type": "code", "metadata": {"id": "VBTKcNUzFNZy"}, "source": ["class VAE(nn.Module):\n", "  def __init__(self,in_dim = 784,z_dim = 20):\n", "    super(VA<PERSON>, self).__init__()\n", "\n", "    self.z_dim = z_dim\n", "\n", "    self.encoder = nn.Sequential(\n", "        nn.Linear(in_dim,in_dim//4),\n", "        nn.ReLU(inplace=True),\n", "        nn.Linear(in_dim//4,in_dim//16),\n", "        nn.ReLU(inplace=True),\n", "        nn.Linear(in_dim//16,z_dim*2),\n", "    )\n", "    self.decoder = nn.Sequential(\n", "        nn.Linear(z_dim,z_dim*4),\n", "        nn.Linear(z_dim*4,z_dim*16),\n", "        nn.Linear(z_dim*16,in_dim),\n", "        nn.<PERSON><PERSON><PERSON><PERSON>(),\n", "    )\n", "\n", "  def forward(self,x):\n", "    x=x.view(x.size(0),-1)\n", "    x = self.encoder(x)\n", "    x = x.view(-1, 2, self.z_dim)\n", "    mu = x[:,0,:]\n", "    log_sigma = x[:,1,:]\n", "    z = mu + torch.randn_like(mu) * torch.exp(log_sigma) \n", "    x_hat = self.decoder(z)\n", "    return x_hat , mu , log_sigma"], "execution_count": null, "outputs": []}, {"cell_type": "code", "metadata": {"id": "hCkmOaTK7oiS"}, "source": ["def criterion(x_hat,mu,log_sigma,x):\n", "  l_rec = L_recon(x_hat,x)\n", "  l_reg = torch.sum(0.5*(torch.exp(log_sigma)**2 + mu**2 - 1 - 2*log_sigma))\n", "  return l_rec + l_reg\n", "\n", "def display_images(x,x_hat,batch_size):\n", "  n = int(np.sqrt(batch_size))\n", "  fig ,axs = plt.subplots(2*n,n, figsize = (15,15))\n", "  x_copy = np.squeeze(x.clone().detach().cpu().numpy())\n", "  x_hat_copy = np.squeeze(x_hat.clone().detach().cpu().numpy())\n", "  \n", "  for i in range(n):\n", "    for j in range(n):\n", "      if len(x.shape)==1:\n", "        axs[i,j].imshow(x_copy[i*n+j].reshape(28,28),cmap='gray')\n", "        axs[i+n,j].imshow(x_hat_copy[i*n+j].reshape(28,28),cmap='gray')\n", "      else:\n", "        axs[i,j].imshow(x_copy[i*n+j],cmap='gray')\n", "        axs[i+n,j].imshow(x_hat_copy[i*n+j],cmap='gray')\n", "\n", "      axs[i,j].axis('off')\n", "      axs[i+n,j].axis('off')\n", "    \n", "  plt.axis('off')\n", "  plt.show()\n", "\n", "def train_model(model):\n", "  avg_loss = 0\n", "  model.train()\n", "  for i, (x,_) in enumerate(train_loader):\n", "    optimizer.zero_grad()\n", "    x = x.to(device)\n", "    x_hat , mu , log_sigma = model(x)\n", "\n", "    ###########################\n", "    if i==0 and epoch%10==0:\n", "      display_images(x,x_hat,batch_size)\n", "    ###########################\n", "    loss = criterion(x_hat,mu,log_sigma,x)\n", "    loss.backward()\n", "    optimizer.step()\n", "    avg_loss += loss.item()\n", "  return avg_loss/len(train_loader.dataset)\n", "\n", "\n", "def validate(model):\n", "  avg_loss = 0\n", "  model.eval()\n", "  with torch.no_grad():\n", "    for x,_ in val_loader:\n", "      x = x.to(device)\n", "      x_hat , mu , sigma = model(x)\n", "      loss = criterion(x_hat,mu,sigma,x)\n", "      avg_loss += loss.data.item()\n", "  \n", "  avg_loss/= len(val_loader.dataset)\n", "  global min_loss \n", "  global j \n", "  \n", "  if min_loss > avg_loss:\n", "    min_loss = avg_loss\n", "    print('\\nMin Loss:{:.4f}'.format(min_loss))\n", "    torch.save(model.state_dict(), f'{path}/model_{type(model).__name__}.pth')\n", "    print('model saved')\n", "    j=1\n", "  else:\n", "    j+=1\n", "    if j==5:\n", "      j=1\n", "      scheduler.step()\n", "      for param_group in optimizer.param_groups:\n", "            print(\"Current learning rate is: {}\".format(param_group['lr']))  \n", "\n", "  return avg_loss"], "execution_count": null, "outputs": []}, {"cell_type": "code", "metadata": {"id": "dOXiNKUZlM1C"}, "source": ["model_1 = VAE(in_dim = 784,z_dim = 20).to(device)\n", "epochs = 100\n", "optimizer = torch.optim.Adam(model_1.parameters(), lr=1e-4)\n", "scheduler = torch.optim.lr_scheduler.StepLR(optimizer, step_size=1, gamma=0.8)\n", "min_loss = 1e6\n", "j = 1\n", "L_recon =  nn.BCELoss(reduction='sum')\n", "for epoch in range(epochs):\n", "  train_loss = train_model(model_1)\n", "  validation_loss = validate(model_1)\n", "  print(f\"Epoch : {epoch} \\nTrain loss : {train_loss:.6f} \\nValidation loss : {validation_loss:.6f}\")"], "execution_count": null, "outputs": []}, {"cell_type": "markdown", "metadata": {"id": "HYFu13B4lEce"}, "source": ["# UNet\n"]}, {"cell_type": "code", "metadata": {"id": "2tnblPrnsCHM"}, "source": ["\n", "def crop(e,d):\n", "  diff = e.size(2) - d.size(2)\n", "  return e[:,:,diff//2:d.size(2)+diff//2 ,diff//2:d.size(2)+diff//2]\n", "\n", "class UNet(nn.<PERSON><PERSON>):\n", "  def __init__(self):\n", "    super(<PERSON><PERSON>, self).__init__()\n", "\n", "    self.maxpool = nn.MaxPool2d(kernel_size=2 , stride = 2)\n", "    self.encoder_conv_1 = nn.Sequential(nn.Conv2d(in_channels= 1, out_channels= 64 , kernel_size= 3),\n", "                                 nn.ReLU(inplace= True),\n", "                                 nn.Conv2d(in_channels= 64, out_channels= 64 , kernel_size= 3),\n", "                                 nn.ReLU(inplace= True),\n", "                                 )\n", "    self.encoder_conv_2 = nn.Sequential(nn.Conv2d(in_channels= 64, out_channels= 128 , kernel_size= 3),\n", "                                 nn.ReLU(inplace= True),\n", "                                 nn.Conv2d(in_channels= 128, out_channels= 128 , kernel_size= 3),\n", "                                 nn.ReLU(inplace= True),\n", "                                 )\n", "    self.encoder_conv_3 = nn.Sequential(nn.Conv2d(in_channels= 128, out_channels= 256 , kernel_size= 3),\n", "                                 nn.ReLU(inplace= True),\n", "                                 nn.Conv2d(in_channels= 256, out_channels= 256 , kernel_size= 3),\n", "                                 nn.ReLU(inplace= True),\n", "                                 )\n", "    # self.encoder_conv_4 = nn.Sequential(nn.Conv2d(in_channels= 256, out_channels= 512 , kernel_size= 3),\n", "    #                             nn.ReLU(inplace= True),\n", "    #                              nn.Conv2d(in_channels= 512, out_channels= 512 , kernel_size= 3),\n", "    #                              nn.ReLU(inplace= True),\n", "    #                              )\n", "    # self.encoder_conv_5 = nn.Sequential(nn.Conv2d(in_channels= 512, out_channels= 1024 , kernel_size= 3),\n", "    #                              nn.ReLU(inplace= True),\n", "    #                              nn.Conv2d(in_channels= 1024, out_channels= 1024 , kernel_size= 3),\n", "    #                              nn.ReLU(inplace= True))    \n", "    \n", "    # self.convt_1= nn.ConvTranspose2d(in_channels = 512, out_channels = 512 , kernel_size=2 ,stride=2)\n", "    # self.convt_2= nn.ConvTranspose2d(in_channels = 512, out_channels = 256 , kernel_size=2,stride=2)\n", "    \n", "    self.convt_3= nn.ConvTranspose2d(in_channels = 128, out_channels = 128 , kernel_size=2,stride=2)\n", "    self.convt_4= nn.ConvTranspose2d(in_channels = 128, out_channels = 64 , kernel_size=2,stride=2)\n", "\n", "    # self.decoder_conv_1 = nn.Sequential(nn.Conv2d(in_channels= 1024, out_channels= 512 , kernel_size= 3),\n", "    #                                     nn.ReLU(inplace= True),\n", "    #                                     nn.Conv2d(in_channels= 512, out_channels= 512 , kernel_size= 3),\n", "    #                                     nn.ReLU(inplace= True),\n", "    #                                     )\n", "    # self.decoder_conv_2 = nn.Sequential(nn.Conv2d(in_channels= 512, out_channels= 256 , kernel_size= 3),\n", "    #                                     nn.ReLU(inplace= True),\n", "    #                                     nn.Conv2d(in_channels= 256, out_channels= 256 , kernel_size= 3),\n", "    #                                     nn.ReLU(inplace= True),\n", "    #                                     )\n", "    self.decoder_conv_3 = nn.Sequential(nn.Conv2d(in_channels= 256, out_channels= 128 , kernel_size= 3),\n", "                                        nn.ReLU(inplace= True),\n", "                                        nn.Conv2d(in_channels= 128, out_channels= 128 , kernel_size= 3),\n", "                                        nn.ReLU(inplace= True),\n", "                                        )\n", "    self.decoder_conv_4 = nn.Sequential(nn.Conv2d(in_channels= 128, out_channels= 64 , kernel_size= 3),\n", "                                        nn.ReLU(inplace= True),\n", "                                        nn.Conv2d(in_channels= 64, out_channels= 64 , kernel_size= 3),\n", "                                        nn.ReLU(inplace= True),\n", "                                        )\n", "    self.decoder_conv_5 = nn.Conv2d(in_channels= 64, out_channels= 1 , kernel_size= 1)\n", "                                      \n", "  def forward(self,x):\n", "      e1 = self.encoder_conv_1(x)\n", "      e2 = self.encoder_conv_2(self.maxpool(e1))\n", "      e3 = self.encoder_conv_3(self.maxpool(e2))\n", "      # e4 = self.encoder_conv_4(self.maxpool(e3))\n", "      # e5 = self.encoder_conv_5(self.maxpool(e4))\n", "      # e5 = e5.view(e5.size(0),2,512,e5.size(2),e5.size(3))\n", "      # mu = e5[:,0,:]\n", "      # log_sigma = e5[:,1,:]\n", "\n", "\n", "      e3 = e3.view(e3.size(0),2,128,e3.size(2),e3.size(3))\n", "      mu = e3[:,0,:]\n", "      log_sigma = e3[:,1,:]\n", "\n", "      z = mu + torch.randn_like(mu) * torch.exp(log_sigma) \n", "      # z = self.decoder_conv_1(torch.cat((self.convt_1(z),crop(e4,self.convt_1(z))),1))\n", "      # z = self.decoder_conv_2(torch.cat((self.convt_2(z),crop(e3,self.convt_2(z))),1))\n", "      z = self.decoder_conv_3(torch.cat((self.convt_3(z),crop(e2,self.convt_3(z))),1))\n", "      z = self.decoder_conv_4(torch.cat((self.convt_4(z),crop(e1,self.convt_4(z))),1))\n", "      z = self.decoder_conv_5(z)\n", "      z = torch.sigmoid(F.interpolate(z, size=x.shape[-2:], mode='bilinear', align_corners=False))\n", "      return z , mu , log_sigma"], "execution_count": null, "outputs": []}, {"cell_type": "code", "metadata": {"id": "w3G3SD1f-03b"}, "source": ["transform = transforms.Compose([transforms.ToTensor(),\n", "                                transforms.<PERSON><PERSON><PERSON>((54,54))\n", "                                ])\n", "batch_size = 64\n", "device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')\n", "train_data = datasets.MNIST(root='../input/data',train=True,download=True,transform=transform)\n", "train_loader = DataLoader(train_data,batch_size=batch_size,shuffle=True)\n", "val_data = datasets.MNIST(root='../input/data',train=False,download=True,transform=transform)\n", "val_loader = DataLoader(val_data,batch_size=batch_size,shuffle=False)"], "execution_count": null, "outputs": []}, {"cell_type": "code", "metadata": {"id": "eIyfXIJJ75s_"}, "source": ["model_2 = UNet().to(device)\n", "epochs = 100\n", "optimizer = torch.optim.Adam(model_2.parameters(), lr=1e-4)\n", "scheduler = torch.optim.lr_scheduler.StepLR(optimizer, step_size=1, gamma=0.8)\n", "L_recon =  nn.BCELoss(reduction='sum')\n", "min_loss = 1e6\n", "j = 1\n", "for epoch in range(epochs):\n", "  train_loss = train_model(model_2)\n", "  validation_loss = validate(model_2)\n", "  print(f\"Epoch : {epoch} \\nTrain loss : {train_loss:.6f} \\nValidation loss : {validation_loss:.6f}\")"], "execution_count": null, "outputs": []}]}