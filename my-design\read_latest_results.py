#!/usr/bin/env python3
"""
读取最新训练结果的脚本
"""

import torch
import os
from pathlib import Path

def read_latest_checkpoint():
    """读取最新的checkpoint文件"""
    # 检查两个可能的路径
    paths = [
        Path("outputs/brats2020/checkpoints/best_model.pth"),
        Path("outputs/brats2020_run/checkpoints/best_model.pth")
    ]
    
    latest_checkpoint = None
    latest_time = 0
    latest_path = None
    
    for path in paths:
        if path.exists():
            mtime = os.path.getmtime(path)
            if mtime > latest_time:
                latest_time = mtime
                latest_path = path
    
    if latest_path is None:
        print("未找到checkpoint文件")
        return None
        
    print(f"读取最新的checkpoint: {latest_path}")
    print(f"文件修改时间: {os.path.getctime(latest_path)}")
    
    try:
        checkpoint = torch.load(latest_path, map_location='cpu')
        return checkpoint, latest_path
    except Exception as e:
        print(f"读取checkpoint失败: {e}")
        return None

def extract_training_results():
    """提取训练结果"""
    result = read_latest_checkpoint()
    if result is None:
        return None
        
    checkpoint, path = result
    
    print("\n" + "="*60)
    print("最新训练结果总结")
    print("="*60)
    
    # 基本信息
    print(f"模型文件: {path}")
    print(f"训练轮数: {checkpoint.get('epoch', 'Unknown')}")
    
    # 模型参数
    if 'model_state_dict' in checkpoint:
        total_params = sum(p.numel() for p in checkpoint['model_state_dict'].values())
        print(f"模型参数总数: {total_params:,}")
    
    # 训练指标
    if 'metrics' in checkpoint:
        metrics = checkpoint['metrics']
        print(f"\n=== 核心性能指标 ===")
        
        # 分割性能
        dice = metrics.get('dice', 0)
        iou = metrics.get('iou', 0)
        sensitivity = metrics.get('sensitivity', 0)
        specificity = metrics.get('specificity', 0)
        
        print(f"Dice分数: {dice:.6f}")
        print(f"IoU分数: {iou:.6f}")
        print(f"敏感性: {sensitivity:.6f}")
        print(f"特异性: {specificity:.6f}")
        
        # VAE性能
        recon_error = metrics.get('reconstruction_error', 0)
        print(f"\nVAE重构误差: {recon_error:.6f}")
        
        # 不确定性量化
        ece = metrics.get('ece', 0)
        unc_corr = metrics.get('uncertainty_correlation', 0)
        print(f"\n不确定性校准误差(ECE): {ece:.6f}")
        print(f"不确定性相关性: {unc_corr:.6f}")
        
        # 异常检测
        anomaly_auc = metrics.get('anomaly_auc', 0)
        anomaly_ap = metrics.get('anomaly_ap', 0)
        print(f"\n异常检测AUC: {anomaly_auc:.6f}")
        print(f"异常检测AP: {anomaly_ap:.6f}")
    
    # 配置信息
    if 'config' in checkpoint:
        config = checkpoint['config']
        print(f"\n=== 训练配置 ===")
        
        if 'training' in config:
            training_config = config['training']
            print(f"批次大小: {training_config.get('batch_size', 'Unknown')}")
            print(f"学习率: {training_config.get('learning_rate', 'Unknown')}")
            print(f"总轮数: {training_config.get('epochs', 'Unknown')}")
            
            if 'loss_weights' in training_config:
                weights = training_config['loss_weights']
                print(f"损失权重: {weights}")
    
    return checkpoint

def read_training_log():
    """读取训练日志的最后几行"""
    log_paths = [
        Path("outputs/brats2020/training.log"),
        Path("outputs/brats2020_run/training.log")
    ]
    
    for log_path in log_paths:
        if log_path.exists():
            print(f"\n=== 训练日志摘要 ({log_path}) ===")
            with open(log_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()
                # 显示最后20行
                for line in lines[-20:]:
                    if 'Epoch' in line or 'Dice' in line or 'Loss' in line or 'completed' in line:
                        print(line.strip())
            break

if __name__ == "__main__":
    # 提取checkpoint结果
    checkpoint = extract_training_results()
    
    # 读取训练日志
    read_training_log()
    
    print("\n" + "="*60)
    print("结果提取完成")
    print("="*60)
