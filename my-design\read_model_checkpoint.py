#!/usr/bin/env python3
"""
脚本用于读取PyTorch模型checkpoint文件中的训练信息
"""

import torch
import os
from pathlib import Path

def read_checkpoint_info(checkpoint_path):
    """读取checkpoint文件中的信息"""
    try:
        # 加载checkpoint
        checkpoint = torch.load(checkpoint_path, map_location='cpu')
        
        print(f"=== 模型Checkpoint信息 ===")
        print(f"文件路径: {checkpoint_path}")
        print(f"文件大小: {os.path.getsize(checkpoint_path) / (1024*1024):.2f} MB")
        print()
        
        # 打印checkpoint中的所有键
        print("Checkpoint包含的键:")
        for key in checkpoint.keys():
            print(f"  - {key}")
        print()
        
        # 提取训练信息
        if 'epoch' in checkpoint:
            print(f"训练轮数: {checkpoint['epoch']}")
        
        if 'best_dice' in checkpoint:
            print(f"最佳Dice分数: {checkpoint['best_dice']:.6f}")
        
        if 'best_loss' in checkpoint:
            print(f"最佳损失: {checkpoint['best_loss']:.6f}")
            
        if 'train_loss' in checkpoint:
            print(f"训练损失: {checkpoint['train_loss']:.6f}")
            
        if 'val_loss' in checkpoint:
            print(f"验证损失: {checkpoint['val_loss']:.6f}")
            
        if 'val_dice' in checkpoint:
            print(f"验证Dice: {checkpoint['val_dice']:.6f}")
            
        if 'val_iou' in checkpoint:
            print(f"验证IoU: {checkpoint['val_iou']:.6f}")
        
        if 'learning_rate' in checkpoint:
            print(f"学习率: {checkpoint['learning_rate']:.8f}")
        
        # 检查优化器状态
        if 'optimizer' in checkpoint:
            print(f"优化器状态: 已保存")
            
        # 检查模型状态
        if 'model_state_dict' in checkpoint:
            model_state = checkpoint['model_state_dict']
            total_params = sum(p.numel() for p in model_state.values())
            print(f"模型参数总数: {total_params:,}")
            
            # 打印模型结构的一些关键层
            print("\n模型关键层:")
            for name, param in list(model_state.items())[:10]:  # 只显示前10层
                print(f"  {name}: {param.shape}")
            if len(model_state) > 10:
                print(f"  ... 还有 {len(model_state) - 10} 层")
        
        # 检查metrics信息
        if 'metrics' in checkpoint:
            metrics = checkpoint['metrics']
            print(f"\n=== 详细训练指标 ===")
            for key, value in metrics.items():
                if isinstance(value, (int, float)):
                    if 'dice' in key.lower() or 'iou' in key.lower():
                        print(f"{key}: {value:.6f}")
                    elif 'loss' in key.lower():
                        print(f"{key}: {value:.6f}")
                    else:
                        print(f"{key}: {value}")
                else:
                    print(f"{key}: {value}")

        # 检查是否有额外的训练历史
        if 'train_history' in checkpoint:
            history = checkpoint['train_history']
            print(f"\n训练历史记录: {len(history)} 个epoch")

        if 'config' in checkpoint:
            config = checkpoint['config']
            print(f"\n=== 配置信息 ===")
            if isinstance(config, dict):
                for key, value in config.items():
                    if key in ['model', 'training', 'data']:
                        print(f"{key}: {value}")
            else:
                print(f"配置信息: 已保存")
            
        return checkpoint
        
    except Exception as e:
        print(f"读取checkpoint时出错: {e}")
        return None

def compare_checkpoints(best_path, latest_path):
    """比较最佳模型和最新模型"""
    print("\n" + "="*50)
    print("比较最佳模型和最新模型")
    print("="*50)
    
    print("\n--- 最佳模型 ---")
    best_checkpoint = read_checkpoint_info(best_path)
    
    print("\n--- 最新模型 ---")
    latest_checkpoint = read_checkpoint_info(latest_path)
    
    # 比较关键指标
    if best_checkpoint and latest_checkpoint:
        print("\n--- 对比总结 ---")
        
        if 'best_dice' in best_checkpoint and 'best_dice' in latest_checkpoint:
            best_dice = best_checkpoint['best_dice']
            latest_dice = latest_checkpoint.get('val_dice', latest_checkpoint.get('best_dice', 0))
            print(f"Dice分数: 最佳={best_dice:.6f}, 最新={latest_dice:.6f}")
            
        if 'epoch' in best_checkpoint and 'epoch' in latest_checkpoint:
            print(f"训练轮数: 最佳模型在第{best_checkpoint['epoch']}轮, 最新模型第{latest_checkpoint['epoch']}轮")

if __name__ == "__main__":
    # 设置文件路径
    base_dir = Path("outputs/brats2020_run/checkpoints")
    best_model_path = base_dir / "best_model.pth"
    latest_model_path = base_dir / "latest_checkpoint.pth"
    
    # 检查文件是否存在
    if not best_model_path.exists():
        print(f"最佳模型文件不存在: {best_model_path}")
        exit(1)
        
    if not latest_model_path.exists():
        print(f"最新模型文件不存在: {latest_model_path}")
        print("只读取最佳模型...")
        read_checkpoint_info(best_model_path)
    else:
        # 比较两个模型
        compare_checkpoints(best_model_path, latest_model_path)
