"""
演示评估脚本
在没有训练模型的情况下演示评估功能
"""

import torch
import numpy as np
import time
from models.model import SegmentationModel
from utils.metrics import SegmentationMetrics
from data.brats2020_dataset import create_brats2020_loaders


def demo_evaluation():
    """演示评估功能"""
    print("🔬 脑肿瘤分割模型评估演示")
    print("=" * 50)
    
    # 设置设备
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"使用设备: {device}")
    
    # 创建模型（随机初始化，仅用于演示）
    print("\n📦 创建模型...")
    model = SegmentationModel(
        input_shape=(240, 240),
        input_channels=4,
        output_channels=1,
        base_channels=32,
        latent_dim=128,
        dropout_rate=0.2
    ).to(device)
    
    model.eval()
    print(f"模型参数数量: {sum(p.numel() for p in model.parameters()):,}")
    
    # 创建演示数据
    print("\n📊 创建演示数据...")
    batch_size = 2
    input_shape = (4, 240, 240)
    
    # 模拟输入数据
    demo_images = torch.randn(batch_size, *input_shape).to(device)
    demo_masks = torch.randint(0, 2, (batch_size, 1, 240, 240)).float().to(device)
    
    print(f"演示图像形状: {demo_images.shape}")
    print(f"演示掩码形状: {demo_masks.shape}")
    
    # 模型推理
    print("\n🔍 执行模型推理...")
    start_time = time.time()
    
    with torch.no_grad():
        seg_output, reconstruction, mu, logvar, uncertainty = model(demo_images)
        
    inference_time = time.time() - start_time
    
    print(f"推理时间: {inference_time:.4f}s")
    print(f"分割输出形状: {seg_output.shape}")
    print(f"重构输出形状: {reconstruction.shape}")
    print(f"不确定性输出形状: {uncertainty.shape}")
    
    # 计算评估指标
    print("\n📈 计算评估指标...")
    
    # 将输出转换为概率
    seg_prob = torch.sigmoid(seg_output)
    seg_pred = (seg_prob > 0.5).float()
    
    # 计算Dice分数
    dice_scores = []
    for i in range(batch_size):
        dice = SegmentationMetrics.dice_coefficient(seg_pred[i], demo_masks[i])
        dice_scores.append(dice)
    
    avg_dice = np.mean(dice_scores)
    
    print(f"平均Dice分数: {avg_dice:.4f}")
    print(f"各样本Dice分数: {[f'{d:.4f}' for d in dice_scores]}")
    
    # 不确定性分析
    uncertainty_mean = uncertainty.mean().item()
    uncertainty_std = uncertainty.std().item()
    
    print(f"\n🎯 不确定性分析:")
    print(f"平均不确定性: {uncertainty_mean:.4f}")
    print(f"不确定性标准差: {uncertainty_std:.4f}")
    
    # 重构质量分析
    recon_mse = torch.nn.functional.mse_loss(reconstruction, demo_images).item()
    print(f"\n🔄 重构质量:")
    print(f"重构MSE损失: {recon_mse:.4f}")
    
    # VAE潜在空间分析
    print(f"\n🧠 潜在空间分析:")
    print(f"潜在均值范围: [{mu.min().item():.4f}, {mu.max().item():.4f}]")
    print(f"潜在方差范围: [{logvar.min().item():.4f}, {logvar.max().item():.4f}]")
    
    print("\n" + "=" * 50)
    print("✅ 评估演示完成！")
    print("\n💡 注意: 这是使用随机初始化模型的演示")
    print("   实际使用时需要加载训练好的模型权重")
    
    return {
        'avg_dice': avg_dice,
        'inference_time': inference_time,
        'uncertainty_mean': uncertainty_mean,
        'recon_mse': recon_mse
    }


def demo_with_real_data():
    """使用真实数据进行演示（如果数据可用）"""
    try:
        print("\n🔄 尝试加载真实数据...")
        
        # 尝试创建数据加载器
        train_loader, val_loader = create_brats2020_loaders(
            data_dir="data",
            batch_size=1,
            num_workers=0,
            image_size=(240, 240)
        )
        
        print("✅ 真实数据加载成功！")
        
        # 获取一个真实样本
        for batch_idx, (images, masks, _) in enumerate(val_loader):
            if batch_idx >= 1:  # 只处理一个批次
                break
                
            print(f"真实数据形状: 图像={images.shape}, 掩码={masks.shape}")
            
            # 使用真实数据进行演示评估
            device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
            model = SegmentationModel(
                input_shape=(240, 240),
                input_channels=4,
                output_channels=1,
                base_channels=32,
                latent_dim=128,
                dropout_rate=0.2
            ).to(device)
            
            model.eval()
            images = images.to(device)
            masks = masks.to(device)
            
            with torch.no_grad():
                seg_output, reconstruction, mu, logvar, uncertainty = model(images)
            
            seg_prob = torch.sigmoid(seg_output)
            dice = SegmentationMetrics.dice_coefficient(seg_prob, masks)

            print(f"真实数据Dice分数: {dice:.4f}")
            break
            
    except Exception as e:
        print(f"⚠️  无法加载真实数据: {e}")
        print("使用模拟数据进行演示...")


if __name__ == "__main__":
    # 运行演示
    results = demo_evaluation()
    
    # 尝试使用真实数据
    demo_with_real_data()
    
    print(f"\n📋 演示结果总结:")
    print(f"   平均Dice分数: {results['avg_dice']:.4f}")
    print(f"   推理时间: {results['inference_time']:.4f}s")
    print(f"   平均不确定性: {results['uncertainty_mean']:.4f}")
    print(f"   重构MSE: {results['recon_mse']:.4f}")
