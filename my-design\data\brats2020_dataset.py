"""
BraTS2020 Dataset Loader for VAE-UNet
专门针对BraTS2020脑肿瘤分割数据集的数据加载器
支持多类别分割和不确定性分析
"""

import logging
import numpy as np
import torch
from torch.utils.data import Dataset
from pathlib import Path
import nibabel as nib
from typing import Optional, Tuple, Dict
from tqdm import tqdm
import albumentations as A
from albumentations.pytorch import ToTensorV2


class BraTS2020Dataset(Dataset):
    """
    BraTS2020脑肿瘤分割数据集
    
    数据结构：
    - T1: native T1-weighted
    - T1ce: post-contrast T1-weighted (T1Gd)
    - T2: T2-weighted
    - FLAIR: T2 Fluid Attenuated Inversion Recovery
    
    标签：
    - 0: Background
    - 1: NCR/NET (Necrotic and Non-enhancing tumor core)
    - 2: ED (Peritumoral edema)
    - 4: ET (GD-enhancing tumor)
    """
    
    def __init__(self, 
                 data_dir: str,
                 split: str = 'train',
                 task: str = 'segmentation',  # 'segmentation', 'survival', 'uncertainty'
                 image_size: Tuple[int, int] = (240, 240),
                 slice_range: Tuple[int, int] = (60, 120),
                 min_tumor_ratio: float = 0.01,
                 max_samples_per_patient: int = 15,
                 augment: bool = True,
                 normalize: bool = True,
                 multi_class: bool = True):  # True: 多类别, False: 二分类
        """
        Args:
            data_dir: BraTS2020数据根目录
            split: 数据集分割 ('train', 'val', 'test')
            task: 任务类型
            image_size: 输出图像尺寸
            slice_range: 有效切片范围
            min_tumor_ratio: 包含肿瘤的最小比例
            max_samples_per_patient: 每个患者最大样本数
            augment: 是否进行数据增强
            normalize: 是否标准化
            multi_class: 是否多类别分割
        """
        self.data_dir = Path(data_dir)
        self.split = split
        self.task = task
        self.image_size = image_size
        self.slice_range = slice_range
        self.min_tumor_ratio = min_tumor_ratio
        self.max_samples_per_patient = max_samples_per_patient
        self.augment = augment and (split == 'train')
        self.normalize = normalize
        self.multi_class = multi_class
        
        # BraTS2020模态
        self.modalities = ['t1', 't1ce', 't2', 'flair']
        
        # 数据增强
        self.transform = self._get_transforms()
        
        # 加载数据
        self.samples = []
        self._load_data()
        
        logging.info(f"Loaded {len(self.samples)} samples for {split} split")
        logging.info(f"Task: {task}, Multi-class: {multi_class}")
    
    def _get_transforms(self):
        """获取数据增强变换"""
        if self.augment:
            return A.Compose([
                A.Resize(self.image_size[0], self.image_size[1]),
                A.HorizontalFlip(p=0.5),
                A.VerticalFlip(p=0.2),
                A.RandomRotate90(p=0.5),
                A.ShiftScaleRotate(
                    shift_limit=0.1,
                    scale_limit=0.1,
                    rotate_limit=15,
                    p=0.5
                ),
                A.ElasticTransform(
                    alpha=1,
                    sigma=50,
                    alpha_affine=50,
                    p=0.3
                ),
                A.GaussNoise(var_limit=(10.0, 50.0), p=0.3),
                A.GaussianBlur(blur_limit=(3, 7), p=0.2),
                A.Normalize(mean=[0.0] * len(self.modalities), 
                           std=[1.0] * len(self.modalities)),
                ToTensorV2()
            ])
        else:
            return A.Compose([
                A.Resize(self.image_size[0], self.image_size[1]),
                A.Normalize(mean=[0.0] * len(self.modalities), 
                           std=[1.0] * len(self.modalities)),
                ToTensorV2()
            ])
    
    def _load_data(self):
        """加载BraTS2020数据索引"""
        # BraTS2020目录结构: BraTS2020_TrainingData/MICCAI_BraTS2020_TrainingData/BraTS20_Training_XXX/
        if self.split == 'train':
            data_path = self.data_dir / 'BraTS2020_TrainingData' / 'MICCAI_BraTS2020_TrainingData'
        elif self.split == 'val':
            data_path = self.data_dir / 'BraTS2020_ValidationData' / 'MICCAI_BraTS2020_ValidationData'
        else:
            data_path = self.data_dir / 'BraTS2020_TestingData' / 'MICCAI_BraTS2020_TestingData'
        
        if not data_path.exists():
            raise ValueError(f"Data directory {data_path} does not exist")
        
        # 获取所有患者目录
        patient_dirs = [d for d in data_path.iterdir() if d.is_dir() and d.name.startswith('BraTS20')]

        # 为了避免内存问题，在验证时只处理前几个患者
        if hasattr(self, '_limit_patients'):
            patient_dirs = patient_dirs[:self._limit_patients]

        for patient_dir in tqdm(patient_dirs, desc=f"Loading {self.split} data"):
            try:
                self._process_patient(patient_dir)
            except Exception as e:
                logging.warning(f"Error processing patient {patient_dir.name}: {e}")
                continue
    
    def _process_patient(self, patient_dir: Path):
        """处理单个患者的数据"""
        patient_id = patient_dir.name
        
        # 检查所有模态文件是否存在
        modality_files = {}
        for modality in self.modalities:
            # 先尝试.nii文件，再尝试.nii.gz文件
            modality_file = patient_dir / f"{patient_id}_{modality}.nii"
            if not modality_file.exists():
                modality_file = patient_dir / f"{patient_id}_{modality}.nii.gz"
                if not modality_file.exists():
                    logging.warning(f"Missing {modality} file for patient {patient_id}")
                    return
            modality_files[modality] = modality_file

        # 检查分割掩码（训练集才有）
        seg_file = patient_dir / f"{patient_id}_seg.nii"
        if not seg_file.exists():
            seg_file = patient_dir / f"{patient_id}_seg.nii.gz"
        if self.split == 'train' and not seg_file.exists():
            logging.warning(f"Missing segmentation file for patient {patient_id}")
            return
        
        # 只加载分割数据用于筛选切片，不加载所有模态数据
        try:
            seg_data = None
            if seg_file.exists():
                seg_nii = nib.load(str(seg_file))
                seg_data = seg_nii.get_fdata()

            # 获取数据维度（只加载一个模态来获取形状）
            first_modality_file = list(modality_files.values())[0]
            temp_nii = nib.load(str(first_modality_file))
            temp_data = temp_nii.get_fdata()

            # 创建虚拟的modality_data字典（只包含形状信息）
            modality_data = {modality: temp_data for modality in self.modalities}

            # 提取2D切片信息
            self._extract_slices(patient_id, modality_data, seg_data)

            # 清理临时数据
            del temp_data, temp_nii
            if seg_data is not None:
                del seg_data

        except Exception as e:
            logging.error(f"Error processing patient {patient_id}: {e}")
    
    def _extract_slices(self, patient_id: str, modality_data: Dict, seg_data: Optional[np.ndarray]):
        """从3D数据中提取2D切片（内存优化版本）"""
        # 获取数据维度 (BraTS数据通常是 240x240x155)
        height, width, depth = list(modality_data.values())[0].shape

        # 确定切片范围
        start_slice = max(0, self.slice_range[0])
        end_slice = min(depth, self.slice_range[1])

        valid_slices = []

        # 筛选有效切片（只检查分割掩码，不保存3D数据）
        for slice_idx in range(start_slice, end_slice):
            if seg_data is not None:
                mask_slice = seg_data[:, :, slice_idx]
                # 计算肿瘤占比
                tumor_ratio = np.sum(mask_slice > 0) / (height * width)
                is_healthy = tumor_ratio == 0
            else:
                # 测试集没有标签，假设都包含肿瘤
                tumor_ratio = 0.1
                is_healthy = False

            # 保存有效切片信息
            if tumor_ratio >= self.min_tumor_ratio or (self.split == 'train' and is_healthy):
                valid_slices.append({
                    'patient_id': patient_id,
                    'slice_idx': slice_idx,
                    'tumor_ratio': tumor_ratio,
                    'is_healthy': is_healthy
                })

        # 限制每个患者的样本数量
        if len(valid_slices) > self.max_samples_per_patient:
            # 优先保留包含肿瘤的切片
            tumor_slices = [s for s in valid_slices if not s['is_healthy']]
            healthy_slices = [s for s in valid_slices if s['is_healthy']]

            if len(tumor_slices) > self.max_samples_per_patient:
                valid_slices = tumor_slices[:self.max_samples_per_patient]
            else:
                remaining = self.max_samples_per_patient - len(tumor_slices)
                valid_slices = tumor_slices + healthy_slices[:remaining]

        # 只存储文件路径，不存储3D数据
        for slice_info in valid_slices:
            # 存储文件路径而不是数据
            patient_dir = Path(self.data_dir) / ('BraTS2020_TrainingData' if self.split == 'train' else 'BraTS2020_ValidationData') / ('MICCAI_BraTS2020_TrainingData' if self.split == 'train' else 'MICCAI_BraTS2020_ValidationData') / patient_id
            slice_info['patient_dir'] = patient_dir
            self.samples.append(slice_info)
    
    def _normalize_slice(self, img_slice: np.ndarray) -> np.ndarray:
        """标准化图像切片"""
        if not self.normalize:
            return img_slice
        
        # 去除异常值
        img_slice = np.clip(img_slice, 
                           np.percentile(img_slice, 1), 
                           np.percentile(img_slice, 99))
        
        # Z-score标准化
        mean = np.mean(img_slice)
        std = np.std(img_slice)
        if std > 0:
            img_slice = (img_slice - mean) / std
        
        # 缩放到[0, 1]
        img_min, img_max = img_slice.min(), img_slice.max()
        if img_max > img_min:
            img_slice = (img_slice - img_min) / (img_max - img_min)
        
        return img_slice.astype(np.float32)
    
    def _process_segmentation_mask(self, mask_slice: np.ndarray) -> np.ndarray:
        """处理分割掩码"""
        if self.multi_class:
            # 多类别分割：保持原始标签 (0, 1, 2, 4)
            # 将标签4映射到3，使其连续
            mask_processed = mask_slice.copy()
            mask_processed[mask_slice == 4] = 3
            return mask_processed.astype(np.float32)
        else:
            # 二分类：所有非零区域都是肿瘤
            return (mask_slice > 0).astype(np.float32)
    
    def __len__(self):
        return len(self.samples)
    
    def __getitem__(self, idx):
        """获取单个样本（运行时加载）"""
        sample_info = self.samples[idx]

        # 运行时加载数据
        patient_id = sample_info['patient_id']
        slice_idx = sample_info['slice_idx']
        patient_dir = sample_info['patient_dir']

        # 加载模态数据
        image_slices = []
        for modality in self.modalities:
            # 尝试加载.nii或.nii.gz文件
            modality_file = patient_dir / f"{patient_id}_{modality}.nii"
            if not modality_file.exists():
                modality_file = patient_dir / f"{patient_id}_{modality}.nii.gz"

            # 加载并提取切片
            nii_data = nib.load(str(modality_file))
            img_3d = nii_data.get_fdata()
            img_slice = img_3d[:, :, slice_idx]
            img_slice = self._normalize_slice(img_slice)
            image_slices.append(img_slice)

        # 合并为多通道图像 [H, W, C]
        multi_modal_image = np.stack(image_slices, axis=-1)

        # 处理掩码
        seg_file = patient_dir / f"{patient_id}_seg.nii"
        if not seg_file.exists():
            seg_file = patient_dir / f"{patient_id}_seg.nii.gz"

        if seg_file.exists():
            seg_nii = nib.load(str(seg_file))
            seg_3d = seg_nii.get_fdata()
            mask_slice = seg_3d[:, :, slice_idx]
            mask_processed = self._process_segmentation_mask(mask_slice)
        else:
            # 测试集没有掩码
            mask_processed = np.zeros((multi_modal_image.shape[0], multi_modal_image.shape[1]), dtype=np.float32)
        
        # 应用数据增强
        if self.transform:
            augmented = self.transform(image=multi_modal_image, mask=mask_processed)
            image = augmented['image']  # [C, H, W]
            mask = augmented['mask']    # [H, W]
            
            # 确保mask是正确的形状
            if len(mask.shape) == 2:
                if self.multi_class:
                    # 多类别：转换为one-hot编码
                    num_classes = 4  # 0, 1, 2, 3 (原来的4)
                    mask_onehot = torch.zeros(num_classes, *mask.shape)
                    for c in range(num_classes):
                        mask_onehot[c] = (mask == c).float()
                    mask = mask_onehot
                else:
                    # 二分类
                    mask = mask.unsqueeze(0)
        else:
            # 手动转换为tensor
            image = torch.from_numpy(multi_modal_image.transpose(2, 0, 1)).float()
            if self.multi_class:
                num_classes = 4
                mask_onehot = torch.zeros(num_classes, *mask_processed.shape)
                for c in range(num_classes):
                    mask_onehot[c] = torch.from_numpy((mask_processed == c).astype(np.float32))
                mask = mask_onehot
            else:
                mask = torch.from_numpy(mask_processed).unsqueeze(0).float()
        
        return {
            'image': image,                           # [C, H, W]
            'mask': mask,                            # [num_classes, H, W] or [1, H, W]
            'patient_id': sample_info['patient_id'],
            'slice_idx': sample_info['slice_idx'],
            'tumor_ratio': sample_info['tumor_ratio'],
            'is_healthy': sample_info['is_healthy']
        }
    
    def get_class_weights(self) -> torch.Tensor:
        """计算类别权重用于处理类别不平衡"""
        if not self.multi_class:
            return torch.tensor([1.0, 1.0])  # 二分类
        
        # 统计各类别像素数量
        class_counts = np.zeros(4)
        
        for sample in self.samples:
            if sample['seg_data'] is not None:
                seg_slice = sample['seg_data'][:, :, sample['slice_idx']]
                seg_processed = self._process_segmentation_mask(seg_slice)
                
                for c in range(4):
                    class_counts[c] += np.sum(seg_processed == c)
        
        # 计算权重（逆频率）
        total_pixels = np.sum(class_counts)
        weights = total_pixels / (4 * class_counts + 1e-8)  # 避免除零
        
        return torch.from_numpy(weights).float()


def create_brats2020_loaders(data_dir: str,
                            batch_size: int = 4,
                            num_workers: int = 4,
                            image_size: Tuple[int, int] = (240, 240),
                            multi_class: bool = True) -> Tuple:
    """
    创建BraTS2020数据加载器
    
    Returns:
        train_loader, val_loader
    """
    from torch.utils.data import DataLoader
    
    # 创建数据集
    train_dataset = BraTS2020Dataset(
        data_dir=data_dir,
        split='train',
        image_size=image_size,
        multi_class=multi_class,
        augment=True
    )
    
    val_dataset = BraTS2020Dataset(
        data_dir=data_dir,
        split='val',
        image_size=image_size,
        multi_class=multi_class,
        augment=False
    )
    
    # 创建数据加载器
    train_loader = DataLoader(
        train_dataset,
        batch_size=batch_size,
        shuffle=True,
        num_workers=num_workers,
        pin_memory=True,
        drop_last=True
    )
    
    val_loader = DataLoader(
        val_dataset,
        batch_size=batch_size,
        shuffle=False,
        num_workers=num_workers,
        pin_memory=True
    )
    
    return train_loader, val_loader


# 使用示例
if __name__ == "__main__":
    import logging
    logging.basicConfig(level=logging.INFO)
    
    # 创建数据集
    dataset = BraTS2020Dataset(
        data_dir='./BraTS2020',
        split='train',
        image_size=(240, 240),
        multi_class=True,
        augment=True
    )
    
    print(f"Dataset size: {len(dataset)}")
    
    if len(dataset) > 0:
        sample = dataset[0]
        print(f"Image shape: {sample['image'].shape}")
        print(f"Mask shape: {sample['mask'].shape}")
        print(f"Patient ID: {sample['patient_id']}")
        print(f"Multi-class: {dataset.multi_class}")
        
        # 计算类别权重
        weights = dataset.get_class_weights()
        print(f"Class weights: {weights}")
