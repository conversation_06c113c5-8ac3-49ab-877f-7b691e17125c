"""
基于BraTS2018冠军方案的改进训练器
集成冠军方案的最佳实践和我们的功能
"""

import logging
import argparse
import yaml
import torch
import torch.optim as optim
from torch.utils.data import DataLoader
from torch.utils.tensorboard import SummaryWriter
import numpy as np
from tqdm import tqdm
from pathlib import Path
import shutil
import time

# 导入自定义模块
from models.model import SegmentationModel
from utils.loss_functions import combined_loss_function, VAEUNetLoss
from utils.metrics import ComprehensiveMetrics
from utils.scheduler import build_scheduler
from data.brats2020_dataset import create_brats2020_loaders


class ModelTrainer:
    """
    脑肿瘤分割模型训练器
    """
    
    def __init__(self, config_path: str):
        self.config_path = config_path
        self.config = self._load_config()
        
        # 设置设备
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
        # 创建输出目录
        self.output_dir = Path(self.config['output']['output_dir'])
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # 设置日志
        self._setup_logging()
        
        # 创建模型
        self.model = self._create_model()
        
        # 创建数据加载器
        self.train_loader, self.val_loader = self._create_data_loaders()
        
        # 创建损失函数和优化器
        self.criterion = self._create_criterion()
        self.optimizer, self.scheduler = self._create_optimizer()
        
        # 创建评估器
        self.evaluator = ComprehensiveMetrics()
        
        # TensorBoard
        self.writer = SummaryWriter(self.output_dir / 'tensorboard')
        
        # 训练状态
        self.current_epoch = 0
        self.best_dice = 0.0
        self.patience_counter = 0
        
        logging.info("BraTS2018 Inspired Trainer initialized successfully!")
    
    def _load_config(self):
        """加载配置文件"""
        with open(self.config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        return config
    
    def _setup_logging(self):
        """设置日志"""
        log_file = self.output_dir / 'training.log'
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file),
                logging.StreamHandler()
            ]
        )
    
    def _create_model(self):
        """创建模型"""
        # 创建分割模型
        image_size = self.config['dataset'].get('image_size', 240)
        model = SegmentationModel(
            input_shape=(image_size, image_size),
            input_channels=self.config['model']['n_channels'],
            output_channels=self.config['model']['n_classes'],
            base_channels=self.config['model'].get('init_channels', 32),
            latent_dim=self.config['model']['latent_dim'],
            dropout_rate=self.config['training'].get('dropout', 0.2)
        )

        model_type = self.config.get('model_type', 'segmentation')
        logging.info(f"Using Segmentation Model (mode: {model_type})")
        
        model = model.to(self.device)
        
        # 打印模型信息
        total_params = sum(p.numel() for p in model.parameters())
        trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
        logging.info(f"Model parameters: {total_params:,} total, {trainable_params:,} trainable")
        
        return model
    
    def _create_data_loaders(self):
        """创建数据加载器"""
        train_loader, val_loader = create_brats2020_loaders(
            data_dir=self.config['dataset']['data_dir'],
            batch_size=self.config['training']['batch_size'],
            num_workers=self.config['training']['num_workers'],
            image_size=(self.config['dataset']['image_size'], self.config['dataset']['image_size']),
            multi_class=self.config['dataset']['multi_class']
        )
        
        logging.info(f"Data loaders created: {len(train_loader)} train batches, {len(val_loader)} val batches")
        return train_loader, val_loader
    
    def _create_criterion(self):
        """创建损失函数"""
        loss_type = self.config.get('loss_type', 'original')  # 默认使用原始损失
        
        if loss_type == 'combined':
            # 使用组合损失函数
            def criterion_func(seg_pred, seg_target, reconstruction, original, mu, logvar, uncertainty):
                loss_dict = combined_loss_function(
                    seg_pred, seg_target, reconstruction, original, mu, logvar,
                    weight=self.config['training']['loss_weights']['reconstruction']
                )
                return loss_dict['loss'], loss_dict

            logging.info("Using combined loss function")
            return criterion_func
        else:
            # 使用原始VAEUNet损失
            criterion = VAEUNetLoss(
                seg_weight=self.config['training']['loss_weights']['segmentation'],
                recon_weight=self.config['training']['loss_weights']['reconstruction'],
                kl_weight=self.config['training']['loss_weights']['kl_divergence'],
                uncertainty_weight=self.config['training']['loss_weights']['uncertainty']
            )
            logging.info("Using original VAEUNet loss function")
            return criterion
    
    def _create_optimizer(self):
        """创建优化器和调度器"""
        # 优化器
        optimizer = optim.Adam(
            self.model.parameters(),
            lr=self.config['training']['learning_rate'],
            weight_decay=self.config['training']['weight_decay']
        )
        
        # 学习率调度器
        scheduler_type = self.config['training'].get('scheduler', 'poly')
        max_epochs = self.config['training']['epochs']
        
        if scheduler_type == 'poly':
            # 使用BraTS2018冠军方案的PolyLR
            scheduler = build_scheduler(
                optimizer, 'poly', max_epochs,
                power=self.config['training'].get('poly_power', 0.9)
            )
            logging.info("Using Polynomial LR scheduler (BraTS2018 style)")
        elif scheduler_type == 'warmup_poly':
            scheduler = build_scheduler(
                optimizer, 'warmup_poly', max_epochs,
                power=self.config['training'].get('poly_power', 0.9),
                warmup_epochs=self.config['training'].get('warmup_epochs', 10)
            )
            logging.info("Using Warmup + Polynomial LR scheduler")
        else:
            # 原始调度器
            scheduler = optim.lr_scheduler.CosineAnnealingLR(optimizer, max_epochs)
            logging.info("Using Cosine Annealing LR scheduler")
        
        return optimizer, scheduler
    
    def train_epoch(self):
        """训练一个epoch"""
        self.model.train()
        total_loss = 0.0
        num_batches = len(self.train_loader)
        
        progress_bar = tqdm(self.train_loader, desc=f'Epoch {self.current_epoch}')
        
        for batch_idx, batch in enumerate(progress_bar):
            images = batch['image'].to(self.device)
            masks = batch['mask'].to(self.device)
            
            # 前向传播
            seg_pred, reconstruction, mu, logvar, uncertainty = self.model(images)
            
            # 计算损失
            if callable(self.criterion):
                # BraTS2018风格的损失函数
                loss, loss_dict = self.criterion(seg_pred, masks, reconstruction, images, mu, logvar, uncertainty)
            else:
                # 原始损失函数
                loss, loss_dict = self.criterion(seg_pred, masks, reconstruction, images, mu, logvar, uncertainty)
            
            # 反向传播
            self.optimizer.zero_grad()
            loss.backward()
            
            # 梯度裁剪
            if self.config['training'].get('gradient_clipping', 0) > 0:
                torch.nn.utils.clip_grad_norm_(self.model.parameters(), 
                                             self.config['training']['gradient_clipping'])
            
            self.optimizer.step()
            
            total_loss += loss.item()
            
            # 更新进度条
            progress_bar.set_postfix({
                'Loss': f"{loss.item():.4f}",
                'Seg': f"{loss_dict.get('seg_loss', 0):.4f}",
                'Recon': f"{loss_dict.get('recon_loss', 0):.4f}",
                'KL': f"{loss_dict.get('KLD', loss_dict.get('kl_loss', 0)):.4f}"
            })
        
        avg_loss = total_loss / num_batches
        return avg_loss
    
    def validate_epoch(self):
        """验证一个epoch"""
        self.model.eval()
        total_loss = 0.0
        all_metrics = []
        
        with torch.no_grad():
            for batch in tqdm(self.val_loader, desc='Validation'):
                images = batch['image'].to(self.device)
                masks = batch['mask'].to(self.device)
                is_healthy = batch['is_healthy'].to(self.device)
                
                # 前向传播
                seg_pred, reconstruction, mu, logvar, uncertainty = self.model(images)
                
                # 计算损失
                if callable(self.criterion):
                    loss, _ = self.criterion(seg_pred, masks, reconstruction, images, mu, logvar, uncertainty)
                else:
                    loss, _ = self.criterion(seg_pred, masks, reconstruction, images, mu, logvar, uncertainty)
                
                total_loss += loss.item()
                
                # 计算评估指标
                try:
                    seg_prob = torch.sigmoid(seg_pred)
                    batch_metrics = self.evaluator.compute_all_metrics(
                        seg_prob, masks, uncertainty, reconstruction, images, is_healthy
                    )
                    all_metrics.append(batch_metrics)
                except Exception as e:
                    logging.warning(f"Error computing metrics: {e}")
        
        # 平均损失和指标
        avg_loss = total_loss / len(self.val_loader)
        
        if all_metrics:
            avg_metrics = {}
            for key in all_metrics[0].keys():
                avg_metrics[key] = np.mean([m[key] for m in all_metrics])
        else:
            avg_metrics = {'dice': 0.0, 'iou': 0.0}
        
        # 记录到TensorBoard
        self.writer.add_scalar('Val/loss', avg_loss, self.current_epoch)
        for key, value in avg_metrics.items():
            self.writer.add_scalar(f'Val/{key}', value, self.current_epoch)
        
        return avg_loss, avg_metrics
    
    def save_checkpoint(self, metrics, is_best=False):
        """保存检查点"""
        checkpoint_dir = self.output_dir / 'checkpoints'
        checkpoint_dir.mkdir(exist_ok=True)
        
        checkpoint = {
            'epoch': self.current_epoch,
            'model_state_dict': self.model.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict(),
            'scheduler_state_dict': self.scheduler.state_dict(),
            'metrics': metrics,
            'config': self.config,
            'best_dice': self.best_dice
        }
        
        # 保存最新检查点
        torch.save(checkpoint, checkpoint_dir / 'latest_checkpoint.pth')
        
        # 保存最佳模型
        if is_best:
            torch.save(checkpoint, checkpoint_dir / 'best_model.pth')
            logging.info(f"Saved best model at epoch {self.current_epoch} with Dice: {self.best_dice:.4f}")
    
    def train(self):
        """主训练循环"""
        logging.info('Starting BraTS2018 Inspired training...')
        start_time = time.time()
        
        for epoch in range(self.config['training']['epochs']):
            self.current_epoch = epoch
            
            # 训练
            train_loss = self.train_epoch()
            
            # 验证
            val_loss, val_metrics = self.validate_epoch()
            
            # 学习率调度
            self.scheduler.step()
            
            # 记录学习率
            current_lr = self.optimizer.param_groups[0]['lr']
            self.writer.add_scalar('Learning_Rate', current_lr, epoch)
            self.writer.add_scalar('Train/loss', train_loss, epoch)
            
            # 日志
            logging.info(f'Epoch {epoch}:')
            logging.info(f'  Train Loss: {train_loss:.4f}')
            logging.info(f'  Val Loss: {val_loss:.4f}')
            logging.info(f'  Val Dice: {val_metrics["dice"]:.4f}')
            logging.info(f'  Val IoU: {val_metrics["iou"]:.4f}')
            logging.info(f'  Learning Rate: {current_lr:.6f}')
            
            # 保存检查点
            is_best = val_metrics['dice'] > self.best_dice
            if is_best:
                self.best_dice = val_metrics['dice']
                self.patience_counter = 0
            else:
                self.patience_counter += 1
            
            self.save_checkpoint(val_metrics, is_best)
            
            # 早停检查
            if (self.config['training']['early_stopping'] and 
                self.patience_counter >= self.config['training']['patience']):
                logging.info(f'Early stopping at epoch {epoch}')
                break
        
        # 训练完成
        total_time = time.time() - start_time
        self.writer.close()
        
        logging.info('Training completed!')
        logging.info(f'Best validation Dice: {self.best_dice:.4f}')
        logging.info(f'Total training time: {total_time/3600:.2f} hours')


def main():
    parser = argparse.ArgumentParser(description='Train BraTS2018 Inspired VAE-UNet')
    parser.add_argument('--config', type=str, default='config/brats2020_config.yaml',
                       help='Path to configuration file')
    parser.add_argument('--model_type', type=str, choices=['original', 'segmentation'],
                       default='segmentation', help='Model type to use')
    parser.add_argument('--loss_type', type=str, choices=['original', 'combined'],
                       default='combined', help='Loss function type to use')
    
    args = parser.parse_args()
    
    # 加载配置并覆盖参数
    with open(args.config, 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)
    
    config['model_type'] = args.model_type
    config['loss_type'] = args.loss_type
    
    # 保存更新的配置
    config_dir = Path(config['output']['output_dir'])
    config_dir.mkdir(parents=True, exist_ok=True)
    
    with open(config_dir / 'config.yaml', 'w', encoding='utf-8') as f:
        yaml.dump(config, f, default_flow_style=False, allow_unicode=True)
    
    # 创建训练器（传入更新后的配置）
    trainer = ModelTrainer(args.config)
    trainer.config['model_type'] = config['model_type']
    trainer.config['loss_type'] = config['loss_type']
    
    # 开始训练
    trainer.train()


if __name__ == '__main__':
    main()
