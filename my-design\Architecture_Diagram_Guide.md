# VAE-UNet融合架构图创建指南

## 🎯 设计目标
创建一个学术级别的架构图，突出三分支融合的核心创新，适用于CVPR/MICCAI等顶级会议。

## 🎨 推荐工具
**主要推荐：draw.io (diagrams.net)**
- 网址：https://app.diagrams.net/
- 优势：免费、专业、高质量导出
- 替代方案：PowerPoint、Visio、LaTeX TikZ

## 📐 整体布局设计

### 布局结构 (16:9比例，适合PPT)
```
┌─────────────────────────────────────────────────────────────┐
│  输入层    │    共享编码器    │      三分支并行处理           │
│  (左侧)    │    (中间)       │      (右侧)                  │
│           │                │  ┌─ 分割分支                   │
│  4模态MRI  │  U-Net Encoder  │  ├─ VAE分支                   │
│           │                │  └─ 不确定性分支               │
└─────────────────────────────────────────────────────────────┘
```

## 🎨 颜色方案 (学术标准)

### 主色调
- **共享编码器**: 深蓝色 (#2E5BBA) - 表示核心创新
- **分割分支**: 绿色 (#28A745) - 表示主要任务
- **VAE分支**: 橙色 (#FD7E14) - 表示生成任务
- **不确定性分支**: 紫色 (#6F42C1) - 表示置信度
- **数据流箭头**: 深灰色 (#495057)
- **背景**: 白色或浅灰色 (#F8F9FA)

### 文字颜色
- **标题**: 黑色 (#000000)
- **尺寸标注**: 深灰色 (#6C757D)
- **分支标签**: 与对应分支同色

## 📊 具体组件设计

### 1. 输入层 (左侧)
```
┌─────────────┐
│    Input    │
│ 4-Modal MRI │
│[B,4,240,240]│
│             │
│  T1  T1ce   │
│  T2  FLAIR  │
└─────────────┘
```

### 2. 共享编码器 (中间)
```
┌─────────────┐    ┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│   Conv1     │    │   Conv2     │    │   Conv3     │    │ Bottleneck  │
│[B,32,240,240]│ → │[B,64,120,120]│ → │[B,128,60,60]│ → │[B,256,30,30]│
│             │    │             │    │             │    │   (conv4d)  │
└─────────────┘    └─────────────┘    └─────────────┘    └─────────────┘
                                                                │
                                                        ┌───────┼───────┐
                                                        │       │       │
                                                        ▼       ▼       ▼
```

### 3. 三分支处理 (右侧)
```
分割分支:                    VAE分支:                     不确定性分支:
┌─────────────┐            ┌─────────────┐              ┌─────────────┐
│ Seg Decoder │            │ Global Pool │              │ Conv Layers │
│[B,256,30,30]│            │[B,256,1,1]  │              │[B,256,30,30]│
│      ↓      │            │      ↓      │              │      ↓      │
│[B,128,60,60]│            │ μ,σ [B,128] │              │[B,128,30,30]│
│      ↓      │            │      ↓      │              │      ↓      │
│[B,64,120,120]│           │ Reparameterize│            │[B,64,30,30] │
│      ↓      │            │      ↓      │              │      ↓      │
│[B,32,240,240]│           │ Reconstruct │              │[B,1,30,30]  │
│      ↓      │            │[B,4,240,240]│              │      ↓      │
│[B,1,240,240]│            └─────────────┘              │ Interpolate │
└─────────────┘                                         │[B,1,240,240]│
                                                        └─────────────┘
```

## 🔧 draw.io创建步骤

### Step 1: 基础设置
1. 打开 https://app.diagrams.net/
2. 选择 "Create New Diagram"
3. 选择 "Blank Diagram"
4. 设置画布大小：A4横向 (297×210mm)

### Step 2: 创建输入层
1. 插入矩形框 (Rectangle)
2. 设置颜色：填充 #E3F2FD，边框 #1976D2
3. 添加文字：
   ```
   Input
   4-Modal MRI
   [B, 4, 240, 240]
   
   T1    T1ce
   T2    FLAIR
   ```
4. 字体：Arial, 12pt, 居中对齐

### Step 3: 创建共享编码器
1. 创建4个矩形，表示编码器层级
2. 颜色：填充 #2E5BBA，文字白色
3. 尺寸：宽度120px，高度80px
4. 标签：
   - Conv1: [B,32,240,240]
   - Conv2: [B,64,120,120]
   - Conv3: [B,128,60,60]
   - Bottleneck: [B,256,30,30]

### Step 4: 创建三分支
1. **分割分支** (绿色 #28A745)
   - 4个矩形，表示上采样层
   - 添加跳跃连接虚线
   
2. **VAE分支** (橙色 #FD7E14)
   - 全局池化圆形
   - μ,σ参数化矩形
   - 重参数化菱形
   - 重构解码器矩形

3. **不确定性分支** (紫色 #6F42C1)
   - 3个卷积层矩形
   - 插值上采样

### Step 5: 添加数据流箭头
1. 使用 "Arrow" 工具
2. 颜色：#495057，粗细2pt
3. 添加关键连接：
   - 输入 → 编码器
   - 编码器层间连接
   - 瓶颈层 → 三分支
   - 跳跃连接 (虚线)

### Step 6: 添加标注和说明
1. 在瓶颈层添加突出标注：
   ```
   ★ Key Innovation
   Shared Feature Extraction
   [B, 256, 30, 30]
   ```

2. 在右下角添加图例：
   ```
   Legend:
   ■ Shared Encoder (11.6M params)
   ■ Segmentation Branch
   ■ VAE Branch  
   ■ Uncertainty Branch
   ```

## 📏 精确尺寸规格

### 组件尺寸
- **输入框**: 100×120px
- **编码器块**: 120×80px
- **分支块**: 100×60px
- **箭头**: 2pt粗细
- **文字**: 标题14pt，标注12pt，尺寸10pt

### 间距规格
- **水平间距**: 40px
- **垂直间距**: 30px
- **分支间距**: 50px

## 🎯 关键视觉元素

### 1. 突出共享编码器
- 使用最深的蓝色
- 添加阴影效果
- 用粗边框强调

### 2. 强调分支点
- 在瓶颈层添加星形标记
- 用不同颜色的箭头指向三分支
- 添加"Key Innovation"标注

### 3. 显示参数效率
- 在标题下方添加：
  ```
  Total Parameters: 11,570,310
  vs. Separate Models: ~31M
  Efficiency Gain: 63%
  ```

## 📤 导出设置

### 高质量导出
1. File → Export as → PNG
2. 设置：
   - DPI: 300 (印刷质量)
   - 背景: 透明或白色
   - 尺寸: 1920×1080px (16:9)

### 多格式导出
- **PNG**: 用于PPT和网页
- **PDF**: 用于LaTeX论文
- **SVG**: 用于矢量编辑

## 🔍 质量检查清单

- [ ] 所有张量尺寸标注正确
- [ ] 颜色对比度足够 (WCAG AA标准)
- [ ] 文字清晰可读 (最小12pt)
- [ ] 箭头方向明确
- [ ] 图例完整
- [ ] 无拼写错误
- [ ] 符合学术规范

## 📚 学术标准参考

### CVPR/MICCAI风格要求
- 简洁明了，避免过度装饰
- 使用标准的深度学习符号
- 张量尺寸用方括号 [B,C,H,W]
- 颜色有意义且一致
- 高对比度，适合黑白打印

## 🚀 PowerPoint快速创建方案

### 如果你更熟悉PowerPoint，这里是快速创建步骤：

#### Step 1: 设置画布
1. 新建PPT，选择16:9比例
2. 删除标题和内容框
3. 插入 → 形状 → 矩形

#### Step 2: 创建模板
```
颜色代码 (直接复制到PPT颜色选择器):
- 共享编码器: RGB(46,91,186) 或 #2E5BBA
- 分割分支: RGB(40,167,69) 或 #28A745
- VAE分支: RGB(253,126,20) 或 #FD7E14
- 不确定性分支: RGB(111,66,193) 或 #6F42C1
```

#### Step 3: 文字设置
- 字体: Calibri 或 Arial
- 标题: 14pt, 粗体
- 标注: 12pt, 常规
- 尺寸: 10pt, 斜体

#### Step 4: 导出高质量图片
1. 文件 → 导出 → 更改文件类型 → PNG
2. 选择"高质量打印(300 DPI)"

## 📱 移动端友好的简化版本

如果需要在手机或平板上展示，可以创建垂直布局版本：
```
┌─────────────┐
│   输入层     │
│  4模态MRI   │
└─────────────┘
       ↓
┌─────────────┐
│ 共享U-Net   │
│   编码器    │
└─────────────┘
       ↓
┌─────────────┐
│ 瓶颈层分支   │
│ [256,30,30] │
└─────────────┘
    ↙  ↓  ↘
┌────┐┌────┐┌────┐
│分割││VAE ││不确│
│分支││分支││定性│
└────┘└────┘└────┘
```

这个指南将帮你创建一个专业的学术级架构图！
