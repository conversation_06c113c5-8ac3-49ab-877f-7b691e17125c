"""
学习率调度器
"""

import torch
from torch.optim import lr_scheduler


class PolyLR(lr_scheduler._LRScheduler):
    """
    多项式学习率衰减调度器

    学习率按照以下公式衰减：
    lr = base_lr * (1 - epoch / max_epoch) ** power

    Args:
        optimizer: 优化器
        max_epoch: 最大训练轮数
        power: 多项式幂次，默认0.9
        last_epoch: 上一个epoch的索引，默认-1
    """
    
    def __init__(self, optimizer, max_epoch, power=0.9, last_epoch=-1):
        self.max_epoch = max_epoch
        self.power = power
        super(PolyLR, self).__init__(optimizer, last_epoch)
    
    def get_lr(self):
        """计算当前学习率"""
        return [base_lr * (1 - self.last_epoch / self.max_epoch) ** self.power
                for base_lr in self.base_lrs]


class WarmupPolyLR(lr_scheduler._LRScheduler):
    """
    带预热的多项式学习率调度器
    在PolyLR基础上添加预热阶段
    
    Args:
        optimizer: 优化器
        max_epoch: 最大训练轮数
        power: 多项式幂次，默认0.9
        warmup_epochs: 预热轮数，默认5
        warmup_factor: 预热因子，默认0.1
        last_epoch: 上一个epoch的索引，默认-1
    """
    
    def __init__(self, optimizer, max_epoch, power=0.9, warmup_epochs=5, 
                 warmup_factor=0.1, last_epoch=-1):
        self.max_epoch = max_epoch
        self.power = power
        self.warmup_epochs = warmup_epochs
        self.warmup_factor = warmup_factor
        super(WarmupPolyLR, self).__init__(optimizer, last_epoch)
    
    def get_lr(self):
        """计算当前学习率"""
        if self.last_epoch < self.warmup_epochs:
            # 预热阶段：线性增长
            alpha = self.last_epoch / self.warmup_epochs
            warmup_factor = self.warmup_factor * (1 - alpha) + alpha
            return [base_lr * warmup_factor for base_lr in self.base_lrs]
        else:
            # 多项式衰减阶段
            return [base_lr * (1 - self.last_epoch / self.max_epoch) ** self.power
                    for base_lr in self.base_lrs]


class CosineAnnealingWarmupLR(lr_scheduler._LRScheduler):
    """
    带预热的余弦退火学习率调度器
    结合预热和余弦退火的优势
    
    Args:
        optimizer: 优化器
        max_epoch: 最大训练轮数
        eta_min: 最小学习率，默认0
        warmup_epochs: 预热轮数，默认5
        warmup_factor: 预热因子，默认0.1
        last_epoch: 上一个epoch的索引，默认-1
    """
    
    def __init__(self, optimizer, max_epoch, eta_min=0, warmup_epochs=5,
                 warmup_factor=0.1, last_epoch=-1):
        self.max_epoch = max_epoch
        self.eta_min = eta_min
        self.warmup_epochs = warmup_epochs
        self.warmup_factor = warmup_factor
        super(CosineAnnealingWarmupLR, self).__init__(optimizer, last_epoch)
    
    def get_lr(self):
        """计算当前学习率"""
        if self.last_epoch < self.warmup_epochs:
            # 预热阶段
            alpha = self.last_epoch / self.warmup_epochs
            warmup_factor = self.warmup_factor * (1 - alpha) + alpha
            return [base_lr * warmup_factor for base_lr in self.base_lrs]
        else:
            # 余弦退火阶段
            import math
            cosine_epoch = self.last_epoch - self.warmup_epochs
            cosine_max_epoch = self.max_epoch - self.warmup_epochs
            
            return [self.eta_min + (base_lr - self.eta_min) *
                    (1 + math.cos(math.pi * cosine_epoch / cosine_max_epoch)) / 2
                    for base_lr in self.base_lrs]


def build_scheduler(optimizer, scheduler_type, max_epoch, **kwargs):
    """
    构建学习率调度器
    
    Args:
        optimizer: 优化器
        scheduler_type: 调度器类型 ('poly', 'warmup_poly', 'cosine_warmup', 'step', 'cosine')
        max_epoch: 最大训练轮数
        **kwargs: 其他参数
    
    Returns:
        学习率调度器
    """
    if scheduler_type == 'poly':
        power = kwargs.get('power', 0.9)
        return PolyLR(optimizer, max_epoch, power)
    
    elif scheduler_type == 'warmup_poly':
        power = kwargs.get('power', 0.9)
        warmup_epochs = kwargs.get('warmup_epochs', 5)
        warmup_factor = kwargs.get('warmup_factor', 0.1)
        return WarmupPolyLR(optimizer, max_epoch, power, warmup_epochs, warmup_factor)
    
    elif scheduler_type == 'cosine_warmup':
        eta_min = kwargs.get('eta_min', 0)
        warmup_epochs = kwargs.get('warmup_epochs', 5)
        warmup_factor = kwargs.get('warmup_factor', 0.1)
        return CosineAnnealingWarmupLR(optimizer, max_epoch, eta_min, warmup_epochs, warmup_factor)
    
    elif scheduler_type == 'step':
        step_size = kwargs.get('step_size', 30)
        gamma = kwargs.get('gamma', 0.1)
        return lr_scheduler.StepLR(optimizer, step_size, gamma)
    
    elif scheduler_type == 'cosine':
        eta_min = kwargs.get('eta_min', 0)
        return lr_scheduler.CosineAnnealingLR(optimizer, max_epoch, eta_min)
    
    else:
        raise ValueError(f"Unsupported scheduler type: {scheduler_type}")


# 使用示例
if __name__ == "__main__":
    import torch.nn as nn
    
    # 创建一个简单的模型用于测试
    model = nn.Linear(10, 1)
    optimizer = torch.optim.Adam(model.parameters(), lr=1e-3)
    
    # 测试不同的调度器
    schedulers = {
        'PolyLR': PolyLR(optimizer, max_epoch=100, power=0.9),
        'WarmupPolyLR': WarmupPolyLR(optimizer, max_epoch=100, warmup_epochs=10),
        'CosineAnnealingWarmupLR': CosineAnnealingWarmupLR(optimizer, max_epoch=100, warmup_epochs=10)
    }
    
    print("学习率调度器测试:")
    for name, scheduler in schedulers.items():
        print(f"\n{name}:")
        for epoch in [0, 5, 10, 20, 50, 90, 99]:
            scheduler.last_epoch = epoch
            lr = scheduler.get_lr()[0]
            print(f"  Epoch {epoch:2d}: LR = {lr:.6f}")
