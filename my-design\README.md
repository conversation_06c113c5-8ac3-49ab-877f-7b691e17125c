# 脑肿瘤分割项目

基于变分自编码器和U-Net的脑肿瘤MRI分割模型，支持不确定性量化。

## 🎯 项目特点

- **多模态输入**: 支持T1、T1ce、T2、FLAIR四种MRI模态
- **VAE-UNet架构**: 结合VAE进行特征学习和重构
- **注意力机制**: 增强重要特征的表达
- **不确定性量化**: 估计模型预测的不确定性
- **BraTS2020适配**: 专门针对BraTS2020数据集优化
- **内存优化**: 运行时加载，避免内存溢出
- **🏆 优化设计**: 集成先进的网络架构和训练策略
- **灵活配置**: 支持多种模型和损失函数配置
- **先进调度器**: PolyLR、WarmupPolyLR等多种学习率策略

## 📋 环境要求

```bash
torch>=1.9.0
torchvision>=0.10.0
nibabel>=3.2.0
albumentations>=1.0.0
tensorboard>=2.7.0
tqdm>=4.62.0
pyyaml>=5.4.0
numpy>=1.21.0
matplotlib>=3.4.0
```

## 🚀 快速开始

### 1. 安装依赖

```bash
pip install -r requirements.txt
```

### 2. 数据准备

下载BraTS2020数据集并解压到 `data/` 目录：

```
data/
├── BraTS2020_TrainingData/
│   └── MICCAI_BraTS2020_TrainingData/
│       ├── BraTS20_Training_001/
│       │   ├── BraTS20_Training_001_flair.nii
│       │   ├── BraTS20_Training_001_t1.nii
│       │   ├── BraTS20_Training_001_t1ce.nii
│       │   ├── BraTS20_Training_001_t2.nii
│       │   └── BraTS20_Training_001_seg.nii
│       └── ...
└── BraTS2020_ValidationData/
    └── MICCAI_BraTS2020_ValidationData/
        └── ...
```

### 3. 环境验证

```bash
# 验证数据结构和加载
python utils/data_validator.py

# 测试训练流程
python test_training.py
```

### 4. 开始使用

#### **🚀 快速启动（推荐）**
```bash
# 运行测试验证环境
python start.py --test

# 开始训练（使用默认优化设置）
python start.py --train

# 自定义训练参数
python start.py --train --epochs 100 --batch_size 4 --learning_rate 1e-4

# 模型评估
python start.py --evaluate
```

#### **🔧 高级使用**
```bash
# 直接使用训练器（更多控制选项）
python train.py --model_type segmentation --loss_type combined

# 运行完整测试套件
python test.py

# 数据验证
python utils/data_validator.py
```

### 5. 监控训练

```bash
# 启动TensorBoard
tensorboard --logdir ./outputs/brats2020_run/tensorboard
```

### 6. 测试和验证

```bash
# 完整功能测试
python test.py

# 数据验证
python utils/data_validator.py
```

## 📁 项目结构

```
my-design/
├── config/
│   └── brats2020_config.yaml          # 项目配置文件
├── data/
│   ├── brats2020_dataset.py           # BraTS2020数据加载器
│   ├── BraTS2020_TrainingData/        # 训练数据
│   └── BraTS2020_ValidationData/      # 验证数据
├── models/
│   └── model.py                       # 脑肿瘤分割模型
├── utils/
│   ├── loss_functions.py              # 损失函数（含优化实现）
│   ├── metrics.py                     # 评估指标（已修复Dice计算）
│   ├── scheduler.py                   # 学习率调度器
│   └── data_validator.py              # 数据验证工具
├── outputs/                           # 训练输出目录
├── start.py                           # 🚀 项目启动器（推荐入口）
├── train.py                           # 训练器
├── test.py                            # 测试套件
└── evaluate.py                        # 模型评估脚本
```

## 🔧 配置说明

主要配置文件：`config/brats2020_config.yaml`

```yaml
# 数据集配置
dataset:
  data_dir: "./data"
  multi_class: false    # 二分类模式

# 模型配置
model:
  n_channels: 4         # 输入通道数（4种MRI模态）
  n_classes: 1          # 输出类别数（二分类）
  latent_dim: 128       # VAE潜在维度

# 训练配置
training:
  batch_size: 2
  learning_rate: 5e-5
  epochs: 100
```

## 📊 性能指标

项目支持多种评估指标：

- **Dice系数**: 分割重叠度量
- **IoU**: 交并比
- **敏感性/特异性**: 分类性能
- **Hausdorff距离**: 边界准确性
- **不确定性量化**: ECE、可靠性图
- **重构误差**: VAE重构质量

## 🎓 技术特色

### 1. **🏆 优化的网络架构**
- **PolyLR调度器**: 多项式学习率衰减，训练更稳定
- **GroupNorm架构**: 相比BatchNorm更适合小批次训练
- **简洁Dice损失**: 高效稳定的损失函数计算
- **残差连接**: 改善深层网络的梯度流动
- **标准化指标**: 与医学图像分割标准一致的评估

### 2. **灵活的模型配置**
- **模块化设计**: 保持功能完整性和可扩展性
- **配置驱动**: 灵活选择模型和训练策略
- **多种损失函数**: 支持不同的训练目标
- **向后兼容**: 无缝升级，不影响现有工作流

### 3. **先进的学习率策略**
- **PolyLR**: 多项式学习率衰减
- **WarmupPolyLR**: 预热+多项式衰减组合
- **CosineAnnealingWarmup**: 现代深度学习最佳实践
- **自适应调整**: 根据训练阶段自动调整

### 4. **内存优化策略**
- 运行时数据加载
- 智能切片筛选
- 高效的数据管道
- 梯度累积支持

### 5. **完善的错误处理**
- 异常捕获和恢复
- 详细的错误日志
- 调试工具支持
- 自动形状调整

## 🐛 故障排除

### 常见问题

1. **内存不足**
   ```bash
   # 减小批次大小
   python start_training.py --batch_size 1
   ```

2. **Dice分数为0**
   ```bash
   # 运行调试工具
   python debug_dice.py
   ```

3. **数据加载失败**
   ```bash
   # 验证数据结构
   python utils/data_validator.py
   ```

## 📚 核心文档

- **`项目开发详解.md`** - 📖 完整的技术实现详解（包含Dice修复记录）
- **`BraTS2018冠军方案学习与改进总结.md`** - 🏆 医学图像分割技术学习总结

## 🤝 贡献

欢迎提交Issue和Pull Request来改进项目。

## 📄 许可证

本项目采用MIT许可证。

## 🙏 致谢

- 医学图像分割领域的研究成果启发
- BraTS2020数据集提供方
- PyTorch和相关开源库的支持

---

**项目状态**: ✅ 完全配置完成，可直接使用

**最后更新**: 2024年12月

**联系方式**: 如有问题请提交Issue
