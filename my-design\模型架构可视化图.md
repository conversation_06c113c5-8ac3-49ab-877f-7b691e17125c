# VAE-UNet融合架构可视化图

## 🎨 完整架构流程图

```mermaid
graph TD
    A[输入: 4模态MRI<br/>B×4×240×240] --> B[Conv1a: 4→32<br/>B×32×240×240]
    B --> C[ConvBlock1b<br/>B×32×240×240]
    C --> D[Downsample1<br/>B×64×120×120]
    D --> E[ConvBlock2a,2b<br/>B×64×120×120]
    E --> F[Downsample2<br/>B×128×60×60]
    F --> G[ConvBlock3a,3b<br/>B×128×60×60]
    G --> H[Downsample3<br/>B×256×30×30]
    H --> I[瓶颈层: ConvBlock4a,4b,4c,4d<br/>B×256×30×30]
    
    %% 特征共享点
    I --> J[分割解码器分支]
    I --> K[VAE解码器分支]
    I --> L[不确定性估计分支]
    
    %% 分割分支
    J --> J1[Upsample4 + Skip Connection<br/>B×128×60×60]
    J1 --> J2[Upsample3 + Skip Connection<br/>B×64×120×120]
    J2 --> J3[Upsample2 + Skip Connection<br/>B×32×240×240]
    J3 --> J4[Output Conv<br/>B×1×240×240]
    
    %% VAE分支
    K --> K1[Feature Encoder<br/>AdaptiveAvgPool2d<br/>B×256×1×1]
    K1 --> K2[Flatten<br/>B×256]
    K2 --> K3[Mean Layer<br/>μ: B×128]
    K2 --> K4[Var Layer<br/>log σ²: B×128]
    K3 --> K5[Reparameterize<br/>z = μ + ε×σ<br/>B×128]
    K4 --> K5
    K5 --> K6[Feature Decoder<br/>B×256×15×15]
    K6 --> K7[4层上采样<br/>B×4×240×240]
    
    %% 不确定性分支
    L --> L1[Conv: 256→128<br/>B×128×30×30]
    L1 --> L2[Conv: 128→64<br/>B×64×30×30]
    L2 --> L3[Conv: 64→1<br/>B×1×30×30]
    L3 --> L4[Interpolate<br/>B×1×240×240]
    
    %% 输出
    J4 --> O1[分割掩码<br/>B×1×240×240]
    K7 --> O2[重构图像<br/>B×4×240×240]
    L4 --> O3[不确定性热图<br/>B×1×240×240]
    K3 --> O4[VAE均值 μ<br/>B×128]
    K4 --> O5[VAE方差 log σ²<br/>B×128]
    
    %% 跳跃连接
    C -.->|Skip Connection| J3
    E -.->|Skip Connection| J2
    G -.->|Skip Connection| J1
    
    %% 样式
    classDef input fill:#e1f5fe
    classDef encoder fill:#f3e5f5
    classDef bottleneck fill:#fff3e0
    classDef branch fill:#e8f5e8
    classDef output fill:#fce4ec
    
    class A input
    class B,C,D,E,F,G,H encoder
    class I bottleneck
    class J,K,L,J1,J2,J3,K1,K2,K3,K4,K5,K6,L1,L2,L3 branch
    class J4,K7,L4,O1,O2,O3,O4,O5 output
```

## 📊 数据流向详细图

```mermaid
flowchart LR
    subgraph Input ["输入层"]
        A1[T1 模态]
        A2[T1ce 模态]
        A3[T2 模态]
        A4[FLAIR 模态]
    end
    
    subgraph Encoder ["共享编码器"]
        B1[Conv Layer 1<br/>32 channels]
        B2[Conv Layer 2<br/>64 channels]
        B3[Conv Layer 3<br/>128 channels]
        B4[Bottleneck<br/>256 channels]
    end
    
    subgraph Branches ["三分支处理"]
        subgraph SegBranch ["分割分支"]
            C1[Decoder 1<br/>128→64]
            C2[Decoder 2<br/>64→32]
            C3[Decoder 3<br/>32→1]
        end
        
        subgraph VAEBranch ["VAE分支"]
            D1[Encoder<br/>256→256]
            D2[μ, log σ²<br/>256→128]
            D3[Reparameterize<br/>z ~ N(μ,σ²)]
            D4[Decoder<br/>128→4×240×240]
        end
        
        subgraph UncBranch ["不确定性分支"]
            E1[Conv 1<br/>256→128]
            E2[Conv 2<br/>128→64]
            E3[Conv 3<br/>64→1]
        end
    end
    
    subgraph Output ["输出层"]
        F1[分割掩码<br/>1×240×240]
        F2[重构图像<br/>4×240×240]
        F3[不确定性图<br/>1×240×240]
        F4[潜在参数<br/>μ, σ]
    end
    
    %% 连接
    A1 --> B1
    A2 --> B1
    A3 --> B1
    A4 --> B1
    
    B1 --> B2
    B2 --> B3
    B3 --> B4
    
    B4 --> C1
    B4 --> D1
    B4 --> E1
    
    C1 --> C2
    C2 --> C3
    C3 --> F1
    
    D1 --> D2
    D2 --> D3
    D3 --> D4
    D4 --> F2
    D2 --> F4
    
    E1 --> E2
    E2 --> E3
    E3 --> F3
    
    %% 跳跃连接
    B1 -.->|Skip| C2
    B2 -.->|Skip| C1
    B3 -.->|Skip| C1
```

## 🔗 特征融合示意图

```mermaid
graph TB
    subgraph SharedFeatures ["共享特征空间"]
        SF[瓶颈特征<br/>B×256×30×30<br/>语义抽象特征]
    end
    
    subgraph TaskSpecific ["任务特定处理"]
        subgraph Task1 ["分割任务"]
            T1F[空间特征<br/>边界信息]
            T1O[分割掩码]
        end
        
        subgraph Task2 ["重构任务"]
            T2F[分布特征<br/>潜在表示]
            T2O[重构图像]
        end
        
        subgraph Task3 ["不确定性任务"]
            T3F[置信度特征<br/>模糊区域]
            T3O[不确定性图]
        end
    end
    
    subgraph LossFunction ["多任务损失"]
        L1[Dice Loss<br/>权重: 1.0]
        L2[Reconstruction Loss<br/>权重: 0.5]
        L3[KL Divergence<br/>权重: 0.1]
        L4[Uncertainty Loss<br/>权重: 0.05]
        LT[Total Loss]
    end
    
    SF --> T1F
    SF --> T2F
    SF --> T3F
    
    T1F --> T1O
    T2F --> T2O
    T3F --> T3O
    
    T1O --> L1
    T2O --> L2
    T2O --> L3
    T3O --> L4
    
    L1 --> LT
    L2 --> LT
    L3 --> LT
    L4 --> LT
    
    %% 反馈连接
    LT -.->|梯度反传| SF
```

## 📐 尺寸变化流程图

```mermaid
graph TD
    A["输入: [2, 4, 240, 240]<br/>4.6 MB"] --> B["Conv1: [2, 32, 240, 240]<br/>36.9 MB"]
    B --> C["Pool1: [2, 64, 120, 120]<br/>18.4 MB"]
    C --> D["Pool2: [2, 128, 60, 60]<br/>9.2 MB"]
    D --> E["Pool3: [2, 256, 30, 30]<br/>4.6 MB"]
    
    E --> F["分割分支"]
    E --> G["VAE分支"]
    E --> H["不确定性分支"]
    
    F --> F1["Up1: [2, 128, 60, 60]<br/>9.2 MB"]
    F1 --> F2["Up2: [2, 64, 120, 120]<br/>18.4 MB"]
    F2 --> F3["Up3: [2, 32, 240, 240]<br/>36.9 MB"]
    F3 --> F4["Out: [2, 1, 240, 240]<br/>1.15 MB"]
    
    G --> G1["Pool: [2, 256, 1, 1]<br/>0.002 MB"]
    G1 --> G2["Linear: [2, 128]<br/>0.001 MB"]
    G2 --> G3["Decode: [2, 256, 15, 15]<br/>1.15 MB"]
    G3 --> G4["Up4x: [2, 4, 240, 240]<br/>4.6 MB"]
    
    H --> H1["Conv: [2, 128, 30, 30]<br/>2.3 MB"]
    H1 --> H2["Conv: [2, 64, 30, 30]<br/>1.15 MB"]
    H2 --> H3["Conv: [2, 1, 30, 30]<br/>0.036 MB"]
    H3 --> H4["Interp: [2, 1, 240, 240]<br/>1.15 MB"]
    
    %% 内存使用标注
    classDef memory1 fill:#e3f2fd
    classDef memory2 fill:#fff3e0
    classDef memory3 fill:#f1f8e9
    
    class A,F4,G4,H4 memory1
    class B,F3 memory2
    class C,F2 memory3
```

## 🎯 关键连接点详解

### 1. 瓶颈层特征分发

```
瓶颈特征 [B, 256, 30, 30]
    ├── 分割解码器 ← 直接使用，保持空间信息
    ├── VAE编码器 ← 全局池化，提取分布特征  
    └── 不确定性估计器 ← 卷积处理，保持空间结构
```

### 2. 跳跃连接机制

```
编码器层级     →     解码器层级
c1 [32, 240, 240] ──→ u2 [32, 240, 240]
c2 [64, 120, 120] ──→ u3 [64, 120, 120]  
c3 [128, 60, 60]  ──→ u4 [128, 60, 60]
c4 [256, 30, 30]  ──→ 瓶颈层 (分支点)
```

### 3. 多任务输出汇聚

```
分割输出: [B, 1, 240, 240] ──┐
重构输出: [B, 4, 240, 240] ──┼── 联合损失函数
不确定性: [B, 1, 240, 240] ──┤
VAE参数: μ[B,128], σ[B,128] ─┘
```

## 📊 参数分布图

```mermaid
pie title 模型参数分布 (总计: 11.57M)
    "共享编码器" : 6.2
    "分割解码器" : 3.1
    "VAE解码器" : 1.8
    "不确定性估计器" : 0.47
```

## 🔄 训练流程图

```mermaid
sequenceDiagram
    participant D as 数据加载器
    participant M as 模型
    participant L as 损失函数
    participant O as 优化器
    
    D->>M: 输入批次 [B,4,240,240]
    M->>M: 共享编码器前向传播
    M->>M: 三分支并行处理
    M->>L: 输出 (分割,重构,μ,σ,不确定性)
    L->>L: 计算多任务损失
    L->>M: 反向传播梯度
    M->>O: 参数更新
    O->>M: 应用梯度
    
    Note over M: 一次前向传播<br/>服务三个任务
    Note over L: 加权损失组合<br/>平衡多任务学习
```

---

**图表说明**:
- 🔵 蓝色: 输入/输出层
- 🟣 紫色: 编码器层  
- 🟠 橙色: 瓶颈层 (关键分支点)
- 🟢 绿色: 分支处理层
- 🔴 红色: 最终输出层
- ➡️ 实线: 主要数据流
- ⋯⋯ 虚线: 跳跃连接
