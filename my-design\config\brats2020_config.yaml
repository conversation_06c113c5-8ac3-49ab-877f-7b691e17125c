# 脑肿瘤分割模型配置文件
# 针对BraTS2020数据集的配置

# Dataset Configuration
dataset:
  name: "BraTS2020"
  data_dir: "./data"  # 更新为实际数据路径
  modalities: ["t1", "t1ce", "t2", "flair"]
  image_size: 240  # BraTS原始尺寸240x240
  slice_range: [60, 120]  # 有效切片范围
  min_tumor_ratio: 0.01
  max_samples_per_patient: 15
  
  # 任务设置
  task: "segmentation"  # segmentation, survival, uncertainty
  multi_class: false    # true: 4类分割, false: 二分类
  
  # 类别定义
  classes:
    background: 0
    ncr_net: 1      # Necrotic and Non-enhancing tumor core
    edema: 2        # Peritumoral edema  
    enhancing: 3    # GD-enhancing tumor (原标签4映射到3)

# Model Configuration
model:
  n_channels: 4      # t1, t1ce, t2, flair
  n_classes: 1       # 二分类分割
  latent_dim: 128
  use_attention: true
  bilinear: true
  init_channels: 32  # 初始通道数

# Model Type Selection
model_type: "segmentation"  # "original" or "segmentation"
loss_type: "combined"      # "original" or "combined"

# VAE特定参数
vae:
  beta: 0.01
  free_bits: 0.5
  latent_injection: "all"  # all, first, last, bottleneck, none

# Training Configuration
training:
  batch_size: 2      # BraTS数据较大，建议小batch
  num_workers: 4
  epochs: 7
  learning_rate: 1.0e-4  # 适中的学习率
  weight_decay: 1.0e-5
  
  # 损失函数权重
  loss_weights:
    segmentation: 1.0
    reconstruction: 0.5  # 增加重构权重
    kl_divergence: 0.1   # 增加KL权重
    uncertainty: 0.05
    
  # 类别权重（处理不平衡）
  class_weights: "auto"  # 自动计算或手动设置 [1.0, 2.0, 1.5, 3.0]
  
  # 优化器设置
  optimizer: "adam"    # 使用Adam优化器
  scheduler: "poly"    # poly, warmup_poly, cosine, step
  poly_power: 0.9      # 多项式学习率衰减的幂次
  warmup_epochs: 10
  
  # 正则化
  dropout: 0.1
  gradient_clipping: 1.0
  
  # 早停
  early_stopping: true
  patience: 30

# BraTS特定的损失函数
loss_functions:
  segmentation:
    - name: "dice"
      weight: 0.4
    - name: "focal"
      weight: 0.3
      alpha: 0.25
      gamma: 2.0
    - name: "tversky"
      weight: 0.3
      alpha: 0.3
      beta: 0.7
  
  reconstruction:
    - name: "mse"
      weight: 0.7
    - name: "ssim"
      weight: 0.3

# 评估指标
evaluation:
  metrics:
    # 分割性能
    - "dice_per_class"
    - "hausdorff_95"
    - "sensitivity"
    - "specificity"
    - "precision"
    
    # 肿瘤分割指标
    - "whole_tumor_dice"      # 所有肿瘤区域
    - "tumor_core_dice"       # 肿瘤核心
    - "enhancing_tumor_dice"  # 增强肿瘤
    
    # 不确定性指标
    - "ece"
    - "uncertainty_correlation"
    - "reliability_diagram"
  
  # 后处理
  post_processing:
    remove_small_objects: true
    min_object_size: 100
    fill_holes: true

# 数据增强（针对医学图像优化）
augmentation:
  spatial:
    horizontal_flip: 0.5
    vertical_flip: 0.2
    rotation: 0.3
    shift_scale_rotate:
      shift_limit: 0.05
      scale_limit: 0.05
      rotate_limit: 10
      p: 0.3
    elastic_transform:
      alpha: 1
      sigma: 50
      p: 0.2
  
  intensity:
    gaussian_noise:
      var_limit: [5.0, 25.0]
      p: 0.2
    gaussian_blur:
      blur_limit: [3, 5]
      p: 0.1
    brightness_contrast:
      brightness_limit: 0.1
      contrast_limit: 0.1
      p: 0.2

# 输出配置
output:
  output_dir: "./outputs/brats2020"
  experiment_name: "vae_unet_brats2020"
  save_predictions: true
  save_uncertainty_maps: true
  save_attention_maps: true
  
  # 可视化
  visualization:
    save_sample_images: true
    samples_per_epoch: 5
    plot_training_curves: true
    plot_class_distribution: true

# 硬件配置
hardware:
  device: "auto"
  mixed_precision: true
  gradient_accumulation_steps: 2  # 模拟更大的batch size
  
# 日志配置
logging:
  log_level: "INFO"
  tensorboard: true
  wandb: false
  save_logs: true
  
  # 监控指标
  monitor_metrics:
    - "val_dice_mean"
    - "val_whole_tumor_dice"
    - "val_uncertainty_ece"

# 推理配置
inference:
  tta: true  # Test Time Augmentation
  tta_transforms: ["horizontal_flip", "vertical_flip"]
  ensemble: false
  uncertainty_samples: 10  # Monte Carlo采样次数

# 评估设置
evaluation_settings:
  submit_format: "nifti"
  evaluation_regions:
    - "whole_tumor"     # 1, 2, 4
    - "tumor_core"      # 1, 4  
    - "enhancing_tumor" # 4
  
  # 后处理策略
  post_process_strategy:
    - "remove_small_components"
    - "morphological_closing"
    - "largest_connected_component"
