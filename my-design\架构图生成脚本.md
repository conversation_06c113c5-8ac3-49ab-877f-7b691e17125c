# Mermaid图表转换为图片的方法

## 🚀 方法一：使用Mermaid CLI（推荐）

### 安装步骤
```bash
# 1. 安装Node.js (如果没有的话)
# 下载地址: https://nodejs.org/

# 2. 安装Mermaid CLI
npm install -g @mermaid-js/mermaid-cli

# 3. 验证安装
mmdc --version
```

### 使用方法
```bash
# 转换为PNG (推荐)
mmdc -i 架构图.mmd -o 架构图.png -w 1920 -H 1080

# 转换为JPG
mmdc -i 架构图.mmd -o 架构图.jpg -w 1920 -H 1080

# 高质量PNG
mmdc -i 架构图.mmd -o 架构图.png -w 2560 -H 1440 -s 2
```

## 📝 创建Mermaid文件

将以下内容保存为 `架构图.mmd` 文件：

```mermaid
graph TD
    A[输入: 4模态MRI<br/>B×4×240×240] --> B[Conv1a: 4→32<br/>B×32×240×240]
    B --> C[ConvBlock1b<br/>B×32×240×240]
    C --> D[Downsample1<br/>B×64×120×120]
    D --> E[ConvBlock2a,2b<br/>B×64×120×120]
    E --> F[Downsample2<br/>B×128×60×60]
    F --> G[ConvBlock3a,3b<br/>B×128×60×60]
    G --> H[Downsample3<br/>B×256×30×30]
    H --> I[瓶颈层: ConvBlock4a,4b,4c,4d<br/>B×256×30×30]
    
    %% 特征共享点
    I --> J[分割解码器分支]
    I --> K[VAE解码器分支]
    I --> L[不确定性估计分支]
    
    %% 分割分支
    J --> J1[Upsample4 + Skip Connection<br/>B×128×60×60]
    J1 --> J2[Upsample3 + Skip Connection<br/>B×64×120×120]
    J2 --> J3[Upsample2 + Skip Connection<br/>B×32×240×240]
    J3 --> J4[Output Conv<br/>B×1×240×240]
    
    %% VAE分支
    K --> K1[Feature Encoder<br/>AdaptiveAvgPool2d<br/>B×256×1×1]
    K1 --> K2[Flatten<br/>B×256]
    K2 --> K3[Mean Layer<br/>μ: B×128]
    K2 --> K4[Var Layer<br/>log σ²: B×128]
    K3 --> K5[Reparameterize<br/>z = μ + ε×σ<br/>B×128]
    K4 --> K5
    K5 --> K6[Feature Decoder<br/>B×256×15×15]
    K6 --> K7[4层上采样<br/>B×4×240×240]
    
    %% 不确定性分支
    L --> L1[Conv: 256→128<br/>B×128×30×30]
    L1 --> L2[Conv: 128→64<br/>B×64×30×30]
    L2 --> L3[Conv: 64→1<br/>B×1×30×30]
    L3 --> L4[Interpolate<br/>B×1×240×240]
    
    %% 输出
    J4 --> O1[分割掩码<br/>B×1×240×240]
    K7 --> O2[重构图像<br/>B×4×240×240]
    L4 --> O3[不确定性热图<br/>B×1×240×240]
    K3 --> O4[VAE均值 μ<br/>B×128]
    K4 --> O5[VAE方差 log σ²<br/>B×128]
    
    %% 跳跃连接
    C -.->|Skip Connection| J3
    E -.->|Skip Connection| J2
    G -.->|Skip Connection| J1
    
    %% 样式
    classDef input fill:#e1f5fe
    classDef encoder fill:#f3e5f5
    classDef bottleneck fill:#fff3e0
    classDef branch fill:#e8f5e8
    classDef output fill:#fce4ec
    
    class A input
    class B,C,D,E,F,G,H encoder
    class I bottleneck
    class J,K,L,J1,J2,J3,K1,K2,K3,K4,K5,K6,L1,L2,L3 branch
    class J4,K7,L4,O1,O2,O3,O4,O5 output
```

## 🌐 方法二：在线转换（最简单）

### Mermaid Live Editor
1. 访问: https://mermaid.live/
2. 粘贴上面的代码
3. 点击右上角的"Actions" → "Download PNG/SVG"

### 其他在线工具
- **Mermaid Chart**: https://www.mermaidchart.com/
- **Kroki**: https://kroki.io/
- **Diagrams.net**: https://app.diagrams.net/ (支持导入Mermaid)

## 🖼️ 方法三：浏览器截图

### Chrome/Edge浏览器
1. 右键点击图表 → "检查元素"
2. 在开发者工具中找到SVG元素
3. 右键SVG → "截图节点"
4. 自动保存为PNG格式

### Firefox浏览器
1. 右键点击图表 → "截图"
2. 选择"保存可见区域"或"保存整个页面"

## 📱 方法四：使用Python脚本

如果你熟悉Python，可以使用这个脚本：

```python
# 安装依赖
# pip install playwright
# playwright install

from playwright.sync_api import sync_playwright

def mermaid_to_image(mermaid_code, output_path):
    with sync_playwright() as p:
        browser = p.chromium.launch()
        page = browser.new_page()
        
        html_content = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <script src="https://cdn.jsdelivr.net/npm/mermaid/dist/mermaid.min.js"></script>
        </head>
        <body>
            <div class="mermaid">
                {mermaid_code}
            </div>
            <script>
                mermaid.initialize({{startOnLoad: true}});
            </script>
        </body>
        </html>
        """
        
        page.set_content(html_content)
        page.wait_for_selector('.mermaid svg')
        page.screenshot(path=output_path, full_page=True)
        browser.close()

# 使用示例
mermaid_code = """
graph TD
    A[输入] --> B[处理]
    B --> C[输出]
"""

mermaid_to_image(mermaid_code, "架构图.png")
```

## 🎨 图片质量设置

### 高质量设置推荐
```bash
# 4K质量 (适合论文)
mmdc -i 架构图.mmd -o 架构图.png -w 3840 -H 2160 -s 2

# 标准质量 (适合演示)
mmdc -i 架构图.mmd -o 架构图.png -w 1920 -H 1080 -s 1.5

# 网页质量 (适合在线展示)
mmdc -i 架构图.mmd -o 架构图.png -w 1280 -H 720 -s 1
```

### 参数说明
- `-w`: 宽度 (像素)
- `-H`: 高度 (像素)  
- `-s`: 缩放因子 (提高清晰度)
- `-b`: 背景色 (如 `#ffffff` 白色)

## 💡 推荐方案

**对于毕业论文**: 使用方法一 (Mermaid CLI) 生成高质量PNG
**对于快速预览**: 使用方法二 (在线工具)
**对于简单需求**: 使用方法三 (浏览器截图)

选择PNG格式，因为它支持透明背景且质量无损，非常适合学术文档。
