"""
数据验证脚本
用于验证BraTS2020数据集是否正确加载和处理
"""

import sys
import logging
from pathlib import Path
import torch

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent.parent))

from data.brats2020_dataset import BraTS2020Dataset


def validate_data_structure(data_dir: str):
    """验证数据目录结构"""
    data_path = Path(data_dir)
    
    print("=== 数据结构验证 ===")
    print(f"数据根目录: {data_path}")
    
    # 检查训练数据
    train_path = data_path / 'BraTS2020_TrainingData' / 'MICCAI_BraTS2020_TrainingData'
    val_path = data_path / 'BraTS2020_ValidationData' / 'MICCAI_BraTS2020_ValidationData'
    
    print(f"训练数据路径: {train_path}")
    print(f"训练数据存在: {train_path.exists()}")
    
    print(f"验证数据路径: {val_path}")
    print(f"验证数据存在: {val_path.exists()}")
    
    if train_path.exists():
        train_patients = [d for d in train_path.iterdir() if d.is_dir() and d.name.startswith('BraTS20')]
        print(f"训练患者数量: {len(train_patients)}")
        
        # 检查第一个患者的文件
        if train_patients:
            first_patient = train_patients[0]
            print(f"\n检查患者: {first_patient.name}")
            
            modalities = ['t1', 't1ce', 't2', 'flair']
            for modality in modalities:
                nii_file = first_patient / f"{first_patient.name}_{modality}.nii"
                nii_gz_file = first_patient / f"{first_patient.name}_{modality}.nii.gz"
                
                if nii_file.exists():
                    print(f"  {modality}: {nii_file.name} ✓")
                elif nii_gz_file.exists():
                    print(f"  {modality}: {nii_gz_file.name} ✓")
                else:
                    print(f"  {modality}: 缺失 ✗")
            
            # 检查分割文件
            seg_file = first_patient / f"{first_patient.name}_seg.nii"
            seg_gz_file = first_patient / f"{first_patient.name}_seg.nii.gz"
            
            if seg_file.exists():
                print(f"  segmentation: {seg_file.name} ✓")
            elif seg_gz_file.exists():
                print(f"  segmentation: {seg_gz_file.name} ✓")
            else:
                print(f"  segmentation: 缺失 ✗")
    
    if val_path.exists():
        val_patients = [d for d in val_path.iterdir() if d.is_dir() and d.name.startswith('BraTS20')]
        print(f"验证患者数量: {len(val_patients)}")


def validate_dataset_loading(data_dir: str):
    """验证数据集加载"""
    print("\n=== 数据集加载验证 ===")
    
    try:
        # 创建训练数据集（只处理前5个患者）
        print("创建训练数据集...")
        train_dataset = BraTS2020Dataset(
            data_dir=data_dir,
            split='train',
            image_size=(240, 240),
            slice_range=(60, 120),
            min_tumor_ratio=0.01,
            max_samples_per_patient=2,  # 减少样本数量以加快验证
            multi_class=True,
            augment=False  # 验证时不使用增强
        )

        # 限制患者数量用于测试
        train_dataset._limit_patients = 5
        train_dataset.samples = []  # 清空现有样本
        train_dataset._load_data()  # 重新加载有限的数据
        
        print(f"训练数据集大小: {len(train_dataset)}")
        
        if len(train_dataset) > 0:
            # 测试第一个样本
            print("测试第一个样本...")
            sample = train_dataset[0]
            
            print(f"图像形状: {sample['image'].shape}")
            print(f"掩码形状: {sample['mask'].shape}")
            print(f"患者ID: {sample['patient_id']}")
            print(f"切片索引: {sample['slice_idx']}")
            print(f"肿瘤比例: {sample['tumor_ratio']:.4f}")
            print(f"是否健康: {sample['is_healthy']}")
            
            # 检查数据类型和范围
            image = sample['image']
            mask = sample['mask']
            
            print(f"图像数据类型: {image.dtype}")
            print(f"图像值范围: [{image.min():.4f}, {image.max():.4f}]")
            print(f"掩码数据类型: {mask.dtype}")
            print(f"掩码值范围: [{mask.min():.4f}, {mask.max():.4f}]")
            
            # 检查掩码类别分布
            if train_dataset.multi_class:
                mask_indices = torch.argmax(mask, dim=0)
                unique_classes = torch.unique(mask_indices)
                print(f"掩码中的类别: {unique_classes.tolist()}")
                
                for cls in unique_classes:
                    count = torch.sum(mask_indices == cls).item()
                    total = mask_indices.numel()
                    print(f"  类别 {cls}: {count} 像素 ({count/total*100:.2f}%)")
        
        # 创建验证数据集
        print("\n创建验证数据集...")
        val_dataset = BraTS2020Dataset(
            data_dir=data_dir,
            split='val',
            image_size=(240, 240),
            slice_range=(60, 120),
            min_tumor_ratio=0.01,
            max_samples_per_patient=5,
            multi_class=True,
            augment=False
        )
        
        print(f"验证数据集大小: {len(val_dataset)}")
        
        return True
        
    except Exception as e:
        print(f"数据集加载失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def validate_dataloader(data_dir: str):
    """验证数据加载器"""
    print("\n=== 数据加载器验证 ===")
    
    try:
        from torch.utils.data import DataLoader
        
        # 创建数据集
        dataset = BraTS2020Dataset(
            data_dir=data_dir,
            split='train',
            image_size=(240, 240),
            max_samples_per_patient=2,  # 减少样本数量
            multi_class=True,
            augment=False
        )
        
        # 创建数据加载器
        dataloader = DataLoader(
            dataset,
            batch_size=2,
            shuffle=False,
            num_workers=0,  # 使用单线程避免问题
            pin_memory=False
        )
        
        print(f"数据加载器批次数: {len(dataloader)}")
        
        # 测试第一个批次
        print("测试第一个批次...")
        for batch_idx, batch in enumerate(dataloader):
            print(f"批次 {batch_idx}:")
            print(f"  图像形状: {batch['image'].shape}")
            print(f"  掩码形状: {batch['mask'].shape}")
            print(f"  患者ID: {batch['patient_id']}")
            print(f"  切片索引: {batch['slice_idx']}")
            
            # 只测试第一个批次
            break
        
        return True
        
    except Exception as e:
        print(f"数据加载器验证失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主函数"""
    # 设置日志
    logging.basicConfig(level=logging.INFO)
    
    # 数据目录
    data_dir = "./data"
    
    print("BraTS2020 数据验证工具")
    print("=" * 50)
    
    # 验证数据结构
    validate_data_structure(data_dir)
    
    # 验证数据集加载
    dataset_ok = validate_dataset_loading(data_dir)
    
    if dataset_ok:
        # 验证数据加载器
        validate_dataloader(data_dir)
    
    print("\n验证完成!")


if __name__ == "__main__":
    main()
