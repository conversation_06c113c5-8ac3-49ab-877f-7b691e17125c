#!/usr/bin/env python3
"""
BraTS2020 VAE-UNet 项目启动器
基于BraTS2018冠军方案的脑肿瘤分割模型

使用方法:
    python start.py --test          # 快速测试
    python start.py --train         # 开始训练
    python start.py --evaluate      # 模型评估
"""

import argparse
import sys
from pathlib import Path

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent))


def main():
    parser = argparse.ArgumentParser(
        description='BraTS2020 VAE-UNet 项目启动器',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  python start.py --test                    # 运行测试
  python start.py --train                   # 开始训练
  python start.py --train --epochs 100     # 自定义训练轮数
  python start.py --evaluate               # 模型评估
        """
    )
    
    # 主要操作
    group = parser.add_mutually_exclusive_group(required=True)
    group.add_argument('--test', action='store_true', help='运行模型和功能测试')
    group.add_argument('--train', action='store_true', help='开始训练模型')
    group.add_argument('--evaluate', action='store_true', help='评估已训练的模型')
    
    # 训练参数
    parser.add_argument('--config', type=str, default='config/brats2020_config.yaml',
                       help='配置文件路径 (默认: config/brats2020_config.yaml)')
    parser.add_argument('--epochs', type=int, help='训练轮数')
    parser.add_argument('--batch_size', type=int, help='批次大小')
    parser.add_argument('--learning_rate', type=float, help='学习率')
    parser.add_argument('--output_dir', type=str, help='输出目录')
    
    # 模型选择
    parser.add_argument('--model_type', type=str,
                       choices=['segmentation', 'original'],
                       default='segmentation',
                       help='模型类型 (默认: segmentation)')
    parser.add_argument('--loss_type', type=str,
                       choices=['combined', 'original'],
                       default='combined',
                       help='损失函数类型 (默认: combined)')
    
    args = parser.parse_args()
    
    print("=" * 60)
    print("🧠 脑肿瘤分割项目")
    print("🏆 基于先进网络架构优化")
    print("=" * 60)
    
    if args.test:
        print("🧪 运行测试...")
        try:
            from test import main as test_main
            test_main()
        except Exception as e:
            print(f"❌ 测试失败: {e}")
            return 1
    
    elif args.train:
        print("🚀 开始训练...")
        try:
            # 构建训练命令参数
            train_args = [
                '--config', args.config,
                '--model_type', args.model_type,
                '--loss_type', args.loss_type
            ]
            
            # 添加可选参数
            if args.epochs:
                train_args.extend(['--epochs', str(args.epochs)])
            if args.batch_size:
                train_args.extend(['--batch_size', str(args.batch_size)])
            if args.learning_rate:
                train_args.extend(['--learning_rate', str(args.learning_rate)])
            if args.output_dir:
                train_args.extend(['--output_dir', args.output_dir])
            
            # 导入并运行训练器
            import train
            
            # 模拟命令行参数
            original_argv = sys.argv
            sys.argv = ['train.py'] + train_args
            
            try:
                train.main()
            finally:
                sys.argv = original_argv
                
        except Exception as e:
            print(f"❌ 训练失败: {e}")
            return 1
    
    elif args.evaluate:
        print("📊 开始评估...")
        try:
            # 检查是否有训练好的模型
            import os
            model_path = "outputs/best_model.pth"
            data_dir = "data"

            if not os.path.exists(model_path):
                print(f"❌ 找不到模型文件: {model_path}")
                print("请先训练模型或指定正确的模型路径")
                return 1

            if not os.path.exists(data_dir):
                print(f"❌ 找不到数据目录: {data_dir}")
                print("请确保数据目录存在")
                return 1

            # 构建评估命令参数
            eval_args = [
                '--model_path', model_path,
                '--data_dir', data_dir,
                '--split', 'val',
                '--batch_size', '2',
                '--image_size', '240'
            ]

            # 导入并运行评估器
            import evaluate

            # 模拟命令行参数
            original_argv = sys.argv
            sys.argv = ['evaluate.py'] + eval_args

            try:
                evaluate.main()
            finally:
                sys.argv = original_argv

        except Exception as e:
            print(f"❌ 评估失败: {e}")
            print("提示: 请确保已经训练了模型并且数据目录正确")
            return 1
    
    print("\n✅ 操作完成！")
    return 0


if __name__ == '__main__':
    exit(main())
