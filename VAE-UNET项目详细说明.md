# VAE-UNET 项目详细说明文档

## 项目概述

VAE-UNET 是一个结合了变分自编码器（Variational Autoencoder, VAE）和 U-Net 架构的深度学习项目，专门用于糖尿病视网膜病变（Diabetic Retinopathy）病灶的图像分割。该模型集成了不确定性量化功能，以提高检测的可靠性。

### 主要特点
- 结合 VAE 和 U-Net 的混合架构
- 支持不确定性量化
- 专门针对眼底图像中的糖尿病视网膜病变病灶分割
- 支持多种病灶类型（如微动脉瘤 MA、硬性渗出 EX 等）
- 集成注意力机制
- 支持多种潜在空间注入策略

## 项目结构详解

### 核心文件

#### 1. `train.py` - 主训练脚本
**功能**: 项目的核心训练文件，负责模型的训练过程
**主要功能**:
- 设置训练参数和超参数
- 数据加载和预处理
- 模型训练循环
- 损失函数计算（包括重构损失和KL散度损失）
- 验证和评估
- 模型检查点保存
- Weights & Biases (wandb) 实验跟踪

**关键特性**:
- 支持混合精度训练（AMP）
- 梯度累积和裁剪
- 早停机制
- KL退火策略
- 多温度采样训练

#### 2. `evaluate.py` - 模型评估脚本
**功能**: 对训练好的模型进行评估
**主要功能**:
- 模型推理
- 计算各种评估指标
- 生成可视化样本
- 支持批量评估

#### 3. `analyze_model.py` - 模型分析工具
**功能**: 分析已训练模型的性能和特性

#### 4. `visualize_vae.py` - VAE可视化工具
**功能**: 可视化VAE的潜在空间和重构结果

### 模型架构文件 (`unet/` 目录)

#### 1. `unet_model.py` - 标准U-Net实现
**功能**: 实现标准的U-Net架构
**特点**:
- 经典的编码器-解码器结构
- 跳跃连接
- 支持双线性插值上采样

#### 2. `unet_resnet.py` - ResNet骨干的VAE-UNet
**功能**: 项目的核心模型文件，实现了基于ResNet骨干的VAE-UNet
**主要组件**:
- **AttentionGate**: 注意力门机制，用于增强特征选择
- **DecoderBlock**: 解码器块，支持潜在空间注入
- **UNetResNet**: 主模型类

**关键特性**:
- 使用timm库的预训练ResNet作为编码器
- VAE潜在空间编码（mu和logvar头）
- 多种潜在空间注入策略：
  - `all`: 在瓶颈层和所有解码器层注入
  - `first`: 仅在瓶颈层和第一个解码器层注入
  - `last`: 仅在瓶颈层和最后一个解码器层注入
  - `bottleneck`: 仅在瓶颈层注入
  - `none`: 不进行潜在空间注入

#### 3. `unet_parts.py` - U-Net组件
**功能**: 定义U-Net的基础组件（如DoubleConv、Down、Up等）

### 工具函数 (`utils/` 目录)

#### 1. `data_loading.py` - 数据加载器
**功能**: 实现IDRID数据集的加载和预处理
**特点**:
- 支持不同病灶类型的数据加载
- 图像缩放和补丁提取
- 数据增强
- 训练/验证/测试集分割

#### 2. `loss.py` - 损失函数
**功能**: 定义各种损失函数
**包含**:
- CombinedLoss: 组合损失函数
- MASegmentationLoss: 微动脉瘤专用损失
- KLAnnealer: KL散度退火器
- kl_with_free_bits: 带自由位的KL散度

#### 3. `metrics.py` - 评估指标
**功能**: 实现各种分割评估指标
**包含**:
- Dice系数
- IoU (Intersection over Union)
- 精确率、召回率
- F1分数等

#### 4. `vae_utils.py` - VAE工具函数
**功能**: VAE相关的实用函数
**包含**:
- 预测生成
- 潜在空间统计计算
- 不确定性量化

#### 5. `uncertainty_metrics.py` - 不确定性指标
**功能**: 计算模型预测的不确定性指标

#### 6. `tensor_utils.py` - 张量工具
**功能**: 张量操作的实用函数

### 配置文件

#### 1. `requirements.txt` - 依赖包列表
**内容**: 项目所需的Python包及版本
- torch: PyTorch深度学习框架
- timm: 预训练模型库
- matplotlib: 可视化
- torchmetrics: 评估指标
- albumentations: 数据增强
- wandb: 实验跟踪
- 等等

#### 2. `sweep.yaml` - 超参数搜索配置
**功能**: 定义Weights & Biases的超参数搜索空间
**包含参数**:
- 学习率范围
- 批次大小
- 图像缩放比例
- 补丁大小
- KL散度相关参数

#### 3. `package-lock.json` - Node.js依赖锁定文件
**功能**: 锁定Node.js依赖版本（可能用于前端可视化）

### 数据目录 (`data/`)

#### 结构:
```
data/
├── imgs/          # 原始图像
│   ├── train/     # 训练集图像
│   ├── val/       # 验证集图像
│   └── test/      # 测试集图像
└── masks/         # 分割掩码
    ├── train/     # 训练集掩码
    ├── val/       # 验证集掩码
    └── test/      # 测试集掩码
```

### 输出目录

#### 1. `outputs/` - 训练输出
**包含**:
- attention: 注意力机制相关输出
- attention512/700: 不同分辨率的注意力输出
- attentionFullScale: 全尺度注意力输出

#### 2. `analysis/` - 分析结果
**包含**: 不同实验配置的分析结果
- EX_T1.0_N10: 硬性渗出，温度1.0，样本数10
- MA_T2.0_N10: 微动脉瘤，温度2.0，样本数10

#### 3. `uncertainty/` - 不确定性分析
**包含**: 各种不确定性量化实验结果

#### 4. `calibration/` - 校准结果
**包含**: 模型校准相关的实验结果

## 如何运行项目

### 1. 环境准备

```bash
# 克隆项目
git clone https://github.com/tmuird/VAE-UNET.git
cd VAE-UNET

# 安装依赖
pip install -r requirements.txt
```

### 2. 数据准备

将IDRID数据集按照以下结构放置：
```
data/
├── imgs/
│   ├── train/     # 训练图像
│   ├── val/       # 验证图像
│   └── test/      # 测试图像
└── masks/
    ├── train/     # 训练掩码
    ├── val/       # 验证掩码
    └── test/      # 测试掩码
```

### 3. 训练模型

#### 基础训练命令:
```bash
python train.py --lesion-type EX --epochs 100 --batch-size 4 --learning-rate 1e-4
```

#### 主要参数说明:
- `--lesion-type`: 病灶类型 (EX, MA, HE, SE)
- `--epochs`: 训练轮数
- `--batch-size`: 批次大小
- `--learning-rate`: 学习率
- `--img-scale`: 图像缩放比例
- `--patch-size`: 补丁大小
- `--use-attention`: 是否使用注意力机制
- `--beta`: KL散度权重
- `--free-bits`: 自由位参数
- `--latent-injection`: 潜在空间注入策略

#### 示例训练命令:
```bash
# 训练硬性渗出分割模型
python train.py \
    --lesion-type EX \
    --epochs 200 \
    --batch-size 4 \
    --learning-rate 1e-4 \
    --img-scale 1.0 \
    --patch-size 512 \
    --use-attention \
    --beta 0.001 \
    --free-bits 0.5 \
    --latent-injection all

# 训练微动脉瘤分割模型
python train.py \
    --lesion-type MA \
    --epochs 200 \
    --batch-size 2 \
    --learning-rate 5e-5 \
    --img-scale 1.0 \
    --patch-size 700 \
    --use-attention \
    --beta 0.0005 \
    --latent-injection first
```

### 4. 模型评估

```bash
python evaluate.py --model-path checkpoints/your_model.pth --lesion-type EX
```

### 5. 超参数搜索

使用Weights & Biases进行超参数搜索：
```bash
wandb sweep sweep.yaml
wandb agent your_sweep_id
```

### 6. 模型分析和可视化

```bash
# 分析模型性能
python analyze_model.py --model-path checkpoints/your_model.pth

# 可视化VAE潜在空间
python visualize_vae.py --model-path checkpoints/your_model.pth
```

## 技术特点

### 1. VAE-UNet架构
- 结合变分自编码器的概率建模能力
- U-Net的精确分割能力
- 潜在空间学习病灶的高级表示

### 2. 不确定性量化
- 通过VAE的随机性提供预测不确定性
- 多次采样获得预测分布
- 有助于临床决策的可靠性评估

### 3. 注意力机制
- 增强模型对重要特征的关注
- 提高小病灶的检测能力

### 4. 多种训练策略
- KL散度退火
- 多温度采样
- 梯度累积和裁剪
- 早停机制

## 应用场景

1. **糖尿病视网膜病变筛查**: 自动检测和分割眼底图像中的病灶
2. **医学图像分析**: 为医生提供辅助诊断工具
3. **研究应用**: 研究不同病灶的特征和分布
4. **不确定性评估**: 评估模型预测的可靠性

这个项目代表了医学图像分割领域的先进技术，特别是在处理小目标病灶和提供预测不确定性方面具有重要价值。

---

## 🧠 **适配MRI肿瘤分割的修改指南**

### **为什么这个项目适合您的研究方向**

这个VAE-UNET项目与您的硕士论文想法**高度匹配**：

✅ **架构完美契合**: 已实现VAE-UNet混合架构，共享编码器设计
✅ **异常检测能力**: VAE分支可用于健康组织分布建模
✅ **不确定性量化**: 通过重构误差和潜在空间距离实现异常评分
✅ **可解释性支持**: 注意力机制类似显著性图功能
✅ **组合损失函数**: 分割损失 + 重构损失 + KL散度损失

### **核心修改策略**

#### **第一阶段：数据适配 (1-2周)**

**1. 创建MRI数据集类**
```python
# 在 utils/data_loading.py 中添加
class MRITumorDataset(Dataset):
    def __init__(self, base_dir, split='train', modality='T1',
                 scale=1.0, patch_size=None, max_images=None):
        """
        Args:
            modality: MRI模态 ('T1', 'T2', 'FLAIR', 'T1ce')
            base_dir: 数据根目录
        """
        # 适配MRI数据格式 (通常是.nii.gz或DICOM)
        # 支持多模态输入
        # 处理3D到2D切片的转换
```

**2. 数据目录结构调整**
```
data/
├── images/
│   ├── train/
│   │   ├── patient001_T1.nii.gz
│   │   ├── patient001_T2.nii.gz
│   │   └── ...
│   ├── val/
│   └── test/
└── masks/
    ├── train/
    │   ├── patient001_seg.nii.gz
    │   └── ...
    ├── val/
    └── test/
```

#### **第二阶段：模型架构调整 (2-3周)**

**1. 修改输入通道数**
```python
# 在 unet/unet_resnet.py 中
class UNetResNet(nn.Module):
    def __init__(self, n_channels=4, ...):  # 支持多模态MRI
        # T1, T2, FLAIR, T1ce = 4个通道
```

**2. 增强异常检测功能**
```python
# 添加健康组织重构分支
class HealthyTissueVAE(nn.Module):
    def __init__(self, latent_dim=128):
        # 专门用于建模健康组织分布
        # 计算重构误差作为异常评分

    def anomaly_score(self, x):
        # 返回异常评分和不确定性
        recon_error = F.mse_loss(reconstruction, x)
        return recon_error, uncertainty
```

#### **第三阶段：损失函数优化 (1周)**

**1. 设计异常检测损失**
```python
# 在 utils/loss.py 中添加
class AnomalyDetectionLoss(nn.Module):
    def __init__(self, alpha=1.0, beta=0.1, gamma=0.01):
        self.seg_loss = DiceLoss()  # 分割损失
        self.recon_loss = nn.MSELoss()  # 重构损失
        self.kl_loss = kl_divergence  # KL散度

    def forward(self, pred_seg, true_seg, recon, original, mu, logvar):
        # 组合损失：分割 + 重构 + KL散度
        total_loss = (alpha * seg_loss +
                     beta * recon_loss +
                     gamma * kl_loss)
```

#### **第四阶段：评估指标扩展 (1周)**

**1. 添加异常检测指标**
```python
# 在 utils/metrics.py 中添加
def calculate_anomaly_metrics(anomaly_scores, true_labels):
    # AUC-ROC for anomaly detection
    # Precision-Recall for tumor detection
    # Uncertainty calibration metrics
    return {
        'auc_roc': auc_roc,
        'auc_pr': auc_pr,
        'ece': expected_calibration_error
    }
```

### **具体实施步骤**

#### **步骤1: 环境准备**
```bash
# 安装额外的MRI处理库
pip install nibabel  # 处理.nii.gz文件
pip install SimpleITK  # 医学图像处理
pip install monai  # 医学AI工具包
```

#### **步骤2: 数据预处理脚本**
```python
# 创建 preprocess_mri.py
def convert_nii_to_slices(nii_path, output_dir):
    """将3D MRI转换为2D切片"""
    # 读取NIfTI文件
    # 提取有意义的切片（包含肿瘤的切片）
    # 标准化和归一化
    # 保存为PNG或NPY格式
```

#### **步骤3: 修改训练脚本**
```python
# 在 train.py 中修改
def main():
    # 替换数据集
    train_dataset = MRITumorDataset(...)

    # 调整模型参数
    model = UNetResNet(
        n_channels=4,  # 多模态MRI
        n_classes=1,   # 二分类：肿瘤/背景
        latent_dim=128,
        use_attention=True
    )

    # 使用新的损失函数
    criterion = AnomalyDetectionLoss()
```

### **推荐的数据集**

1. **BraTS (Brain Tumor Segmentation)**
   - 多模态MRI (T1, T1ce, T2, FLAIR)
   - 标准化的肿瘤分割标注
   - 年度挑战赛数据

2. **TCGA-GBM/TCGA-LGG**
   - 胶质瘤MRI数据
   - 包含基因组信息

3. **MICCAI数据集**
   - 各种医学图像分割挑战

### **实验设计建议**

#### **对比实验**
1. **Baseline**: 标准U-Net
2. **VAE-UNet**: 当前项目架构
3. **您的方法**: VAE-UNet + 异常检测增强

#### **评估维度**
1. **分割性能**: Dice, IoU, Hausdorff距离
2. **异常检测**: AUC-ROC, AUC-PR
3. **不确定性量化**: 校准误差, 可靠性图
4. **可解释性**: 注意力图, 显著性分析

### **时间规划建议**

- **第1-2周**: 数据预处理和数据集适配
- **第3-4周**: 模型架构修改和调试
- **第5-6周**: 损失函数优化和训练
- **第7-8周**: 实验和结果分析
- **第9-10周**: 论文撰写和完善

### **潜在挑战和解决方案**

1. **3D到2D的信息损失**
   - 解决方案: 使用2.5D方法（相邻切片作为多通道输入）

2. **类别不平衡**
   - 解决方案: 焦点损失、加权采样

3. **小肿瘤检测**
   - 解决方案: 多尺度训练、注意力机制

这个项目为您的研究提供了**优秀的起点**，核心架构已经实现，您只需要专注于MRI特定的适配和异常检测功能的增强。
