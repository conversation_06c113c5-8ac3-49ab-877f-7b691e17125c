augmentation:
  intensity:
    brightness_contrast:
      brightness_limit: 0.1
      contrast_limit: 0.1
      p: 0.2
    gaussian_blur:
      blur_limit:
      - 3
      - 5
      p: 0.1
    gaussian_noise:
      p: 0.2
      var_limit:
      - 5.0
      - 25.0
  spatial:
    elastic_transform:
      alpha: 1
      p: 0.2
      sigma: 50
    horizontal_flip: 0.5
    rotation: 0.3
    shift_scale_rotate:
      p: 0.3
      rotate_limit: 10
      scale_limit: 0.05
      shift_limit: 0.05
    vertical_flip: 0.2
dataset:
  classes:
    background: 0
    edema: 2
    enhancing: 3
    ncr_net: 1
  data_dir: ./data
  image_size: 240
  max_samples_per_patient: 15
  min_tumor_ratio: 0.01
  modalities:
  - t1
  - t1ce
  - t2
  - flair
  multi_class: false
  name: BraTS2020
  slice_range:
  - 60
  - 120
  task: segmentation
evaluation:
  metrics:
  - dice_per_class
  - hausdorff_95
  - sensitivity
  - specificity
  - precision
  - whole_tumor_dice
  - tumor_core_dice
  - enhancing_tumor_dice
  - ece
  - uncertainty_correlation
  - reliability_diagram
  post_processing:
    fill_holes: true
    min_object_size: 100
    remove_small_objects: true
evaluation_settings:
  evaluation_regions:
  - whole_tumor
  - tumor_core
  - enhancing_tumor
  post_process_strategy:
  - remove_small_components
  - morphological_closing
  - largest_connected_component
  submit_format: nifti
hardware:
  device: auto
  gradient_accumulation_steps: 2
  mixed_precision: true
inference:
  ensemble: false
  tta: true
  tta_transforms:
  - horizontal_flip
  - vertical_flip
  uncertainty_samples: 10
logging:
  log_level: INFO
  monitor_metrics:
  - val_dice_mean
  - val_whole_tumor_dice
  - val_uncertainty_ece
  save_logs: true
  tensorboard: true
  wandb: false
loss_functions:
  reconstruction:
  - name: mse
    weight: 0.7
  - name: ssim
    weight: 0.3
  segmentation:
  - name: dice
    weight: 0.4
  - alpha: 0.25
    gamma: 2.0
    name: focal
    weight: 0.3
  - alpha: 0.3
    beta: 0.7
    name: tversky
    weight: 0.3
loss_type: combined
model:
  bilinear: true
  init_channels: 32
  latent_dim: 128
  n_channels: 4
  n_classes: 1
  use_attention: true
model_type: segmentation
output:
  experiment_name: vae_unet_brats2020
  output_dir: ./outputs/brats2020
  save_attention_maps: true
  save_predictions: true
  save_uncertainty_maps: true
  visualization:
    plot_class_distribution: true
    plot_training_curves: true
    samples_per_epoch: 5
    save_sample_images: true
training:
  batch_size: 2
  class_weights: auto
  dropout: 0.1
  early_stopping: true
  epochs: 7
  gradient_clipping: 1.0
  learning_rate: 0.0001
  loss_weights:
    kl_divergence: 0.1
    reconstruction: 0.5
    segmentation: 1.0
    uncertainty: 0.05
  num_workers: 4
  optimizer: adam
  patience: 30
  poly_power: 0.9
  scheduler: poly
  warmup_epochs: 10
  weight_decay: 1.0e-05
vae:
  beta: 0.01
  free_bits: 0.5
  latent_injection: all
